'use client'
import React, { useEffect } from 'react'

import { useAuth } from '@/app/config/firebaseConfig'
import { loadingAtom, retailerAtom } from '@/customComponents/providers'
import { useAtom, useAtomValue } from 'jotai'
import { redirect, useRouter } from 'next/navigation'
const AuthChecker: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const user = useAuth()
  const loading = useAtomValue(loadingAtom)
  const [retailer, setRetailer] = useAtom(retailerAtom)
  const router = useRouter()
  useEffect(() => {
    if (!loading && user == null) {
      setRetailer((prev) => ({
        customerId: '',
        customerCode: '',
        customerName: '',
        contactName: '',
        GSTNo: '',
        companyName: '',
        mobileNumber: '',
        Email: '',
        billingAddress: '',
        businessVertical: '',
        customerType: '',
        Status: '',
        rowId: '',
      }))
      redirect('/login')
    }

    // auth.signOut();
  }, [user, loading, setRetailer])

  return <>{children}</>
}

export default AuthChecker
