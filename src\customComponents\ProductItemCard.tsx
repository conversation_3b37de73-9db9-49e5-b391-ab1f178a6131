'use client'
import React from 'react'

interface ProductCardProps {
  title: string
  subtitle: string
  description: string
  imageUrl: string
  productTag: string
}

const ProductCard: React.FC<ProductCardProps> = ({ title, subtitle, description, imageUrl, productTag }) => {
  return (
    <div className="relative flex w-full items-start rounded-md border border-gray-200 bg-white shadow-sm mt-3">
      {productTag && (
        <div className={`absolute right-0 top-0 rounded ${productTag !== 'NEW ARRIVAL' ?  'bg-yellow-100'  : 'bg-blue-100'} px-2 py-1 text-xs font-semibold ${ productTag !== 'NEW ARRIVAL' ?  'text-yellow-800'  : 'text-blue-800'}`}>
          {productTag}
        </div>
      )}

      <div className="relative w-32 flex-shrink-0">
        {/* If your image is in /public, you can use Image from 'next/image' */}
        <img src={imageUrl} alt={subtitle} className="h-32 w-32 object-cover" p-2 />
      </div>

      {/* Product Info */}
      <div className="ml-3 flex flex-col self-center ">
        <p className="text-sm font-bold text-[#949494] mt-3 sm:mt-0">{title}</p>
        <h2 className="text-xl font-semibold text-gray-800">{subtitle}</h2>
        <p className="mt-1 text-base text-[#616161] mb-3 sm:mb-0">{description}</p>
      </div>
    </div>
  )
}

export default ProductCard
