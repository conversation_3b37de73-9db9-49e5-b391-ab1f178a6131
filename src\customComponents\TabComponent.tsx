// components/TabComponent.tsx
'use client'
import { useAtom } from 'jotai'
import { selectedTabAtom } from './providers'

interface TabProps {
  tabs: {
    label: string
    count: number
  }[]
}

const TabComponent = ({ tabs }: TabProps) => {
  const [activeTab, setActiveTab] = useAtom(selectedTabAtom)

  return (
    <div className="w-full border-b border-gray-200">
      <div className="flex">
        {tabs.map((tab, index) => (
          <button
            key={index}
            onClick={() => setActiveTab(index)}
            className={`relative flex-1 px-4 py-2 text-center text-sm font-bold transition-colors duration-200 ${
              activeTab === index ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <span className="text-lg">
               {tab.label} {/*({tab.count}) */}
            </span>
            {activeTab === index && <div className="absolute bottom-0 left-0 h-0.5 w-full bg-blue-600" />}
          </button>
        ))}
      </div>
    </div>
  )
}

export default TabComponent
