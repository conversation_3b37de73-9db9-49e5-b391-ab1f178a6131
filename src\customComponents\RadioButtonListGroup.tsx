interface ButtonListItem {
  value: string
  label: string
}
export const RadioButtonListGroup = ({
  buttonList,
  selectedValue,
  setSelectedValue,
}: {
  buttonList: ButtonListItem[]
  selectedValue: string
  setSelectedValue: React.Dispatch<React.SetStateAction<string>>
}) => {
  const handleRadioChange = (value: string) => {
    setSelectedValue(value) // Updates state when a radio button is selected
  }
  return (
    <ul className="items-left flex w-full flex-row gap-3 text-sm font-medium text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:flex sm:space-x-6">
      {buttonList.map((notificationMethod, index) => (
        <li key={notificationMethod.value}>
          <div className="flex items-center">
            <input
              id={notificationMethod.value}
              type="radio"
              name="radio-group"
              value={notificationMethod.value}
              checked={selectedValue === notificationMethod.value}
              onChange={() => handleRadioChange(notificationMethod.value)}
              className="mb-4 h-4 w-4 border-gray-300 bg-gray-100 text-blue-600 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-blue-600 dark:focus:ring-offset-gray-700"
            />
            <label
              htmlFor={notificationMethod.value}
              className="mb-4 ml-2 w-full text-sm font-medium text-gray-900 dark:text-gray-300"
            >
              {notificationMethod.label}
            </label>
          </div>
        </li>
      ))}
    </ul>
  )
}
