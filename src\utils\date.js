const moment = require('moment')
const moment_tz = require('moment-timezone')

export function findCurrentMonth() {
  const currentDate = new Date()
  const currentMonth = currentDate.getMonth() + 1 // Adding 1 because getMonth() returns 0 for January
  return currentMonth.toFixed(0).padStart(2, '0')
}
export function findPreviousMonth() {
  const currentDate = new Date()
  let previousMonth = currentDate.getMonth() // This gives the current month (0 for January, 11 for December)

  // If current month is January (0), set previousMonth to December (11)
  if (previousMonth === 0) previousMonth = 12
  else previousMonth - 1
  return previousMonth.toFixed(0).padStart(2, '0')
}

export function getFinancialYearStartDate() {
  const today = moment().tz('Asia/Kolkata') // Get the current date with Asia/Kolkata timezone
  const currentMonth = today.month() // Get the current month (0-11)

  let financialYearStart
  if (currentMonth >= 3) {
    // If it's April (3) or later, the financial year starts this year
    financialYearStart = moment.tz('Asia/Kolkata').startOf('year').month(3).date(1).format() // Set to April 1st of this year
  } else {
    // Otherwise, the financial year started last year
    financialYearStart = moment.tz('Asia/Kolkata').subtract(1, 'year').startOf('year').month(3).date(1).format() // Set to April 1st of last year
  }
  return financialYearStart
}

// give the financial start year liek "2025"
export function getCurrentFinancialYearStart() {
  // Get the current date
  const today = new Date();

  // Get the current calendar year (e.g., 2025)
  const currentYear = today.getFullYear();

  // Get the current month (0 = January, 1 = February, ..., 11 = December)
  const currentMonth = today.getMonth();

  let financialYearStartYear;

  // The Indian financial year starts in April (month index 3).
  // If the current month is April (3) or later (up to December, 11),
  // the financial year starts in the current calendar year.
  if (currentMonth >= 3) {
    financialYearStartYear = currentYear;
  } else {
    // If the current month is January (0), February (1), or March (2),
    // the financial year started in the previous calendar year.
    financialYearStartYear = currentYear - 1;
  }

  // Return the financial year in the format YYYY
  return financialYearStartYear.toString();

}

// give the financial start year liek "2025"
export function getCurrentFinancialYearEnd() {
  // Get the current date
  const today = new Date();

  // Get the current calendar year (e.g., 2025)
  const currentYear = today.getFullYear();

  // Get the current month (0 = January, 1 = February, ..., 11 = December)
  const currentMonth = today.getMonth();

  let financialYearEndYear;

  // The Indian financial year starts in April (month index 3).
  // If the current month is April (3) or later (up to December, 11),
  // the financial year starts in the current calendar year.
  if (currentMonth >= 3) {
    financialYearEndYear = currentYear + 1;
  } else {
    // If the current month is January (0), February (1), or March (2),
    // the financial year started in the previous calendar year.
    financialYearEndYear = currentYear;
  }

  // Return the financial year in the format YYYY
  return financialYearEndYear.toString();

}

function getNumberOfWeeksInAMonth(year, month) {
  const lastDay = moment(`${(month, year, '01')}`).endOf('month')
  return lastDay.isoWeek()
}

export function getfilteredData(data, selectedValue, type) {
  if (!data) return []
  if (selectedValue == 'All') {
    const today = moment()
    const currentFinancialYear = today.month() >= 3 ? today.year() : today.year() - 1 // Determine the financial year
    const financialYearStart = moment(`${currentFinancialYear}-04-01`, 'YYYY-MM-DD') // Financial year starts on April 1st
    const monthsValuesArray = Array(today.diff(financialYearStart, 'months') + 1).fill(0)
    const years = [currentFinancialYear]

    if (today.year() != currentFinancialYear) {
      years.push(today.year())
    }

    years.forEach((year) => {
      if (!data[year]) return

      data[year].forEach((item) => {
        const monthKey = Object.keys(item)[0] // Extract month as string
        const month = parseInt(monthKey, 10) - 1 // Convert to integer here April 4 but, our index is 3
        const salesData = item[monthKey]

        let adjustedMonth = 0
        if (month < 3) {
          adjustedMonth = 12 - (2 - month)
        } else {
          adjustedMonth = month - 3
        }

        // Populate sales data in monthsSalesArray
        if (type === 'purchases') {
          monthsValuesArray[adjustedMonth] = salesData.totalSales
        } else if (type === 'payments') {
          monthsValuesArray[adjustedMonth] = salesData.totalPayment
        }
      })
    })
console.log("monthsValuesArray : ", monthsValuesArray)
    return monthsValuesArray
  } else {
    let weekSalesArray = []
    let selectedDate = selectedValue === 'Last Month' ? moment().subtract(1, 'month') : moment()
    weekSalesArray = Array(getNumberOfWeeksInAMonth(selectedDate.year(), selectedDate.month())).fill(0)
    data[selectedDate?.year()]?.forEach((salesData) => {
      if (salesData.hasOwnProperty(selectedDate.month() + 1)) {
        salesData[selectedDate.month() + 1].weeks.forEach((data) => {
          const key = Object.keys(data)[0]
          weekSalesArray[key - 1] = data[key]
        })
      }
    })

    return weekSalesArray
  }
}

export function getMonthsLabels() {
  const labels = []
  let currentYear = moment().year() // Get the current year
  const currentMonth = moment().month() // Get the current month index (0-11)
  let totalMonths = 0

  if (currentMonth < 3) {
    currentYear -= 1
    totalMonths = 12 - (2 - currentMonth)
  } else {
    totalMonths = currentMonth - 2
  }

  // Loop through the 12 months of the financial year
  for (let i = 0; i < totalMonths; i++) {
    // Add the formatted month to the array
    labels.push(
      moment()
        .year(currentYear)
        .month(3 + i)
        .format('MMM YY')
    )
  }

  return labels // Return the array of months for the current financial year
}

export function getWeeksInMonth(month, year) {
  const startOfMonth = moment(`${year}-${month}-01`)
  const endOfMonth = startOfMonth.clone().endOf('month')
  const totalDays = endOfMonth.date() // Get total days in the month
  const weeks = []

  for (let day = 1; day <= totalDays; day += 7) {
    const weekNumber = Math.ceil(day / 7)
    weeks.push(`Week ${weekNumber}`)
  }
  return weeks
}

export function getLabelsInSelectedMonth(selectedValue) {
  // Determine the end date based on the selected value
  const endDate =
    selectedValue === 'Current Month'
      ? moment().endOf('month').date()
      : moment().subtract(1, 'month').endOf('month').date()

  // Generate weeks
  const labels = Array.from({ length: Math.ceil(endDate / 7) }, (_, i) => `Week ${i + 1}`)
  return labels
}
