'use client'
import { BrandsModal } from '@/customComponents/BrandsModal'
import { RulesModal } from '@/customComponents/RulesModal'
import { indianCurrencyFormat } from '@/utils/formats'
import { calculatePercentageCompleted, calculateTicketsWon } from '@/utils/paymentsUtils'
import { Button } from '@headlessui/react'
import Image from 'next/image'
import { useState } from 'react'

const RewardSchemeQ3 = ({ totalPurchase, totalPayment, supportPersion }) => {
  const [openEligibleProducts, setOpenEligibleProducts] = useState(false)
  const [openRules, setOpenRules] = useState(false)

  const brandsList = [
    'Dr.GrowTea Seed Powder*AC-3902-IMP  $ Upto 50% $ brand',
    'Aqua C+*AC-5600 $ Upto 50% $ brand',
    'Aqua K+*AC-5601 $ Upto 50% $ brand',
  ]

  const ticketsWon = calculateTicketsWon(totalPurchase, totalPayment)

  let purchasePending = 600000 * (ticketsWon + 1) - totalPurchase
  purchasePending = purchasePending <= 0 ? 0 : purchasePending

  let paymentPending = 600000 * (ticketsWon + 1) - totalPayment
  paymentPending = paymentPending <= 0 ? 0 : paymentPending

  const percentCompeted = calculatePercentageCompleted(purchasePending, paymentPending)

  const handleButtonClick = (phoneNumber) => {
    window.open(`tel:${phoneNumber}`, '_blank')
  }

  return (
    <div className="mb-5 flex w-full flex-col rounded-lg border border-gray-200 bg-gray-50 pb-3">
      <div className="flex h-32 w-full rounded-tl rounded-tr bg-scheme-pattern-mobile bg-contain bg-no-repeat md:bg-scheme-pattern-tab lg:bg-scheme-pattern" />
      <div className="pl-3 pr-3">
        <div className="flex-1 pt-3 sm:pt-0">
          <span className="text-base font-bold text-blue-600">Fly to Bali, Indonesia</span>
          <span className="font-inter text-base font-normal text-blue-600"> by completing the sales numbers.</span>
          <br />
          <span className="font-inter text-base font-normal">Talk to your Aquaconnect Officer,</span>
          <br />
          <button
            onClick={() => handleButtonClick(supportPersion?.mobile.slice(-10)) ?? ''}
            className="text-start font-inter text-base font-normal"
          >
            <div className="mt-1 flex flex-row items-center gap-2 sm:mt-1">
              <Image alt="" src="/images/phone_icon.svg" className="size-8" width={8} height={8} />
              <div className="gap flex flex-col -space-y-0.5 text-sm">
                <div className="mt-0 font-bold">{supportPersion?.userName.split('-')[1] ?? ''}</div>
                <div className="mt-0 text-slate-400">{supportPersion?.mobile.slice(-10) ?? ''}</div>
              </div>
            </div>
          </button>
        </div>
        <div className="flex flex-col sm:flex-row sm:pt-5">
          <div className="flex w-full flex-col pr-1 sm:mr-8 sm:w-5/12 sm:pr-0">
            <div className="pb-1 pt-5 text-base font-bold sm:pt-0">Your Perfomance</div>
            <div className="flex flex-row">
              <div className="flex-1 text-[14px] font-normal text-[#6B7280]">Ordered Amount</div>
              <div className="text-right text-sm font-normal">{indianCurrencyFormat(totalPurchase)}</div>
            </div>
            {/*  <div className="flex flex-row">
              <div className="flex-1 text-[14px] font-normal text-[#6B7280]">Dr.Grow Order Value</div>
              <div className="text-right text-sm font-normal">{indianCurrencyFormat(drGrowPurchase)}</div>
            </div> */}
            <div className="flex flex-row">
              <div className="flex-1 text-[14px] font-normal text-[#6B7280]">Payment Completed</div>
              <div className="text-right text-sm font-normal">{indianCurrencyFormat(totalPayment)}</div>
            </div>
            {/* display tickets */}
            {ticketsWon > 0 && (
              <div className="mt-5 flex flex-row gap-3">
                {Array.from({ length: ticketsWon }, (_, i) => (
                  <Image
                    key={i} // Provide a unique key for each image
                    src={`/scheme/bali_ticket_${i + 1}.svg`} // Image file in the public folder
                    alt="ticket"
                    width={112}
                    height={48}
                  />
                ))}
              </div>
            )}
            <div className="mt-2 inline-flex flex-row items-center gap-5">
              {/*  <Button
                className="inline-flex items-center justify-center text-sm font-medium text-[#1644CE]"
                onClick={() => setOpenEligibleProducts(true)}
              >
                Eligible Products
              </Button> */}
              <Button
                className="inline-flex items-center justify-center text-sm font-medium text-[#1644CE]"
                onClick={() => setOpenRules(true)}
              >
                Rules
              </Button>
            </div>
          </div>

          <div className="mt-5 w-full rounded-lg bg-white p-5 leading-loose sm:mr-3 sm:mt-0 sm:w-7/12 sm:px-3 sm:pt-3">
            <div className="text-sm font-bold">
              To get {ticketsWon == 0 ? `1st` : ticketsWon == 1 ? `2nd` : '3rd'} Ticket
            </div>
            <div className="-space-y-1 text-[14px] text-slate-400">
              <div>
                Order products worth <span className="font-bold text-black">{indianCurrencyFormat(purchasePending)}</span>
              </div>
              {/*  <div>
                Dr Grow for <span className="font-bold text-black">{indianCurrencyFormat(drGrowPending)}</span>
              </div> */}
              <div>
                Pay <span className="font-bold text-black">{indianCurrencyFormat(paymentPending)}</span>
              </div>
            </div>

            {/* Dotted Progress Bar */}
            <div className="relative mt-11 h-[2px] rounded-full">
              <div
                className="absolute inset-0 bg-transparent"
                style={{
                  backgroundImage: `repeating-linear-gradient(90deg, #ccc 0, #ccc 6px, transparent 2px, transparent 12px)`,
                }}
              ></div>
              {/* Progress fill */}
              <div
                className="absolute h-[2px] rounded-full bg-blue-500"
                style={{ width: percentCompeted + '%' }} // Adjust this value to control the progress
              ></div>
              {/* Walking man icon */}
              <div
                className="absolute -translate-x-1/2 -translate-y-8"
                style={{ left: `${percentCompeted > 87 ? 87 : percentCompeted}%` }} // Match this with the progress width
              >
                <Image
                  src="/images/walking_man.svg" // Add the walking man icon in the `public` folder
                  alt="Walking Man"
                  width={20}
                  height={32}
                />
              </div>

              {/* Destination */}
              <div className="top-0 -translate-y-8 translate-x-[90%]">
                <Image
                  src="/scheme/bali_flight_icon.svg" // Add the plane icon in the `public` folder
                  alt="Plane"
                  width={43}
                  height={27}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      {openEligibleProducts && (
        <BrandsModal open={openEligibleProducts} setOpen={setOpenEligibleProducts} brandsList={brandsList} />
      )}
      {openRules && <RulesModal open={openRules} setOpen={setOpenRules} brandsList={brandsList} />}
    </div>
  )
}

export default RewardSchemeQ3
