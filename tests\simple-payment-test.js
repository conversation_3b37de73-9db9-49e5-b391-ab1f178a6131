/**
 * Simple Zoho Payment API Test
 * 
 * A lightweight test script that doesn't require external dependencies
 */

const BASE_URL = 'http://localhost:3000';

// Test data
const testPaymentData = {
  amount: 1000.50,
  invoiceNo: 'TEST-INV-001',
  customerId: 'TEST-CUST-001',
  customerName: 'Test Customer',
  customerEmail: '<EMAIL>',
  customerPhone: '+919876543210',
  redirectUrl: 'https://example.com/payment/success',
  referenceId: 'TEST-REF-001'
};

/**
 * Make HTTP request
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data: data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test Health Check
 */
async function testHealthCheck() {
  console.log('\n=== Testing Health Check ===');
  
  const result = await makeRequest(`${BASE_URL}/api/zoho/health`);
  
  if (result.success) {
    console.log('✅ Health check passed');
    console.log('Response:', JSON.stringify(result.data, null, 2));
    return true;
  } else {
    console.log('❌ Health check failed');
    console.log('Error:', result.error || result.data);
    return false;
  }
}

/**
 * Test Legacy Initiate Payment API
 */
async function testLegacyInitiatePayment() {
  console.log('\n=== Testing Legacy Initiate Payment API ===');
  
  const result = await makeRequest(`${BASE_URL}/api/initiatePayment`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(testPaymentData)
  });
  
  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data || result.error, null, 2));
  
  if (result.success && result.data.result === 'success') {
    console.log('✅ Legacy initiate payment API works');
    return true;
  } else {
    console.log('❌ Legacy initiate payment API failed');
    return false;
  }
}

/**
 * Test Invalid Data Handling
 */
async function testInvalidDataHandling() {
  console.log('\n=== Testing Invalid Data Handling ===');
  
  const invalidData = { invoiceNo: 'TEST-INV-002' }; // Missing amount
  
  const result = await makeRequest(`${BASE_URL}/api/initiatePayment`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(invalidData)
  });
  
  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data || result.error, null, 2));
  
  if (result.status === 400 && result.data.error) {
    console.log('✅ Invalid data handling works correctly');
    return true;
  } else {
    console.log('❌ Invalid data handling failed');
    return false;
  }
}

/**
 * Test New Payment Session API
 */
async function testNewPaymentSession() {
  console.log('\n=== Testing New Payment Session API ===');
  
  const newPaymentData = {
    amount: testPaymentData.amount,
    currency: 'INR',
    description: 'Test payment for aquaculture products',
    invoice_number: testPaymentData.invoiceNo,
    customer_id: testPaymentData.customerId,
    customer_name: testPaymentData.customerName,
    customer_email: testPaymentData.customerEmail,
    customer_phone: testPaymentData.customerPhone,
    redirect_url: testPaymentData.redirectUrl,
    reference_id: testPaymentData.referenceId,
    meta_data: [
      { key: 'product_type', value: 'aquaculture' },
      { key: 'order_id', value: 'ORD-TEST-001' }
    ]
  };
  
  const result = await makeRequest(`${BASE_URL}/api/zoho/payments/create-session`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(newPaymentData)
  });
  
  console.log('Status:', result.status);
  console.log('Response:', JSON.stringify(result.data || result.error, null, 2));
  
  if (result.success && result.data.success) {
    console.log('✅ New payment session API works');
    return true;
  } else {
    console.log('❌ New payment session API failed');
    return false;
  }
}

/**
 * Test Zoho Payment GET API
 */
async function testZohoPaymentGET() {
  console.log('\n=== Testing Zoho Payment GET API ===');
  
  const url = `${BASE_URL}/api/zohoPayment?amount=${testPaymentData.amount}&invoice=${testPaymentData.invoiceNo}`;
  
  try {
    const response = await fetch(url, { redirect: 'manual' });
    
    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.status === 302 || response.status === 301) {
      const location = response.headers.get('location');
      console.log('✅ Zoho Payment GET API redirects correctly');
      console.log('Redirect Location:', location);
      return true;
    } else {
      console.log('❌ Zoho Payment GET API did not redirect as expected');
      return false;
    }
  } catch (error) {
    console.log('❌ Zoho Payment GET API failed:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Simple Zoho Payment API Tests');
  console.log('==========================================');
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Legacy Initiate Payment', fn: testLegacyInitiatePayment },
    { name: 'Invalid Data Handling', fn: testInvalidDataHandling },
    { name: 'New Payment Session', fn: testNewPaymentSession },
    { name: 'Zoho Payment GET', fn: testZohoPaymentGET }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log('\n==========================================');
  console.log('📊 TEST SUMMARY');
  console.log('==========================================');
  console.log(`Total Tests: ${tests.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`);
  
  if (passed === tests.length) {
    console.log('\n🎉 All tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.');
  }
  
  return { passed, failed, total: tests.length };
}

// Check if running directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testHealthCheck,
    testLegacyInitiatePayment,
    testInvalidDataHandling,
    testNewPaymentSession,
    testZohoPaymentGET
  };
}
