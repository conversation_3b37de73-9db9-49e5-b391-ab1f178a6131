import { Schema, model, models } from 'mongoose'

const CreditNotesSchema = new Schema(
  {
    customerId: String,
    customerName: String,
    date: String,
    date1: String,
    creditNoteId: String,
    creditNoteNumber: String,
    invoiceId: String,
    productId: String,
    itemName: String,
    brand: String,
    itemCategory: String,
    categoryType: String,
    retailType: String,
    invoiceRaisedBy: String,
    businessVertical: String,
    customerType: String,
    ao: String,
    asm: String,
    quantity: String,
    amount: String,
    purchaseValue: String,
    purchasePrice: String,
    margin: String,
    tag: String,
    category: String,
    week: String,
  },
  { collection: 'CreditNotes' }
)

const CreditNotes = models.CreditNotes || model('CreditNotes', CreditNotesSchema)

export default CreditNotes
