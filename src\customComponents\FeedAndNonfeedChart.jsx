'use clilent'
import {
  <PERSON><PERSON><PERSON>roller,
  BarElement,
  CategoryScale,
  Chart as ChartJ<PERSON>,
  Legend,
  LineController,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
  elements,
  plugins,
} from 'chart.js'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { Chart } from 'react-chartjs-2'
import { getMonthsArray } from './../utils/extras'
import { dashboardAtom } from './providers'

ChartJS.register(
  CategoryScale,
  LinearScale,
  LineController,
  BarController,
  LineElement,
  PointElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  plugins,
  elements
)

const generateChartOption = ({ type, title = '' } = {}) => {
  const option = {
    type: type || 'bar',
    responsive: true,
    aspectRatio: 1.6,
    borderColor: 'rgba(127, 86, 217, 1)',
    borderDash: [5, 5],
    elements: {
      point: {
        pointStyle: false,
      },
    },
    interaction: {
      mode: 'index',
    },
    plugins: {
      legend: {
        display: true,
      },
      title: {
        display: true,
        text: title, // title,
        align: 'start',
        color: '#374151',
        padding: {
          top: 0,
          left: 30,
          bottom: 10,
          right: 10,
        },
        font: {
          size: 18,
          weight: '500',

          family: 'inter',
        },
      },
      tooltip: {
        titleFont: {},
        bodyFont: {},
        displayColors: false,
        backgroundColor: 'grey',
        textColor: 'white',
        yAlign: 'bottom',
        callbacks: {
          labelTextColor: (ctx) => {
            return 'white'
          },
          title: (ctx) => {
            return `${ctx[0].label}`
          },
        },
      },
    },

    hover: {
      mode: 'label',
    },
    gridLines: {
      display: false,
    },
    scales: {
      x: {
        stacked: true,
        display: true,
        scaleLabel: {
          display: true,
        },
        grid: {
          display: false,
        },
      },
      y: {
        stacked: true,
        beginAtZero: true,
        border: {
          dash: [3, 5],
        },
        grid: {
          display: true,
          drawOnChartArea: true,
          drawTicks: true,
          lineWidth: 2,
        },
        ticks: {
          stepSize: 1000,
          callback: function (value, index, values) {
            if (value >= 1000) {
              return value / 1000 + 'K'
            } else {
              if (value == 0) {
                return '0 K'
              } else {
                value / 1000 + 'K'
              }
            }
            return value
          },
        },
      },
    },
  }
  return option
}

const generateDataSet = ({ type, label, values, color = 'rgba(127, 86, 217, 1)' } = {}) => {
  const dataSet = {
    type: type || 'bar',
    label: label || 'Purchase',
    data: values || [102189, 47748, 0, 104496, 163864, 155013, 147225],

    backgroundColor:
      type !== 'line'
        ? (context) => {
            const ctx = context.chart.ctx
            const gradient = ctx.createLinearGradient(0, 0, 0, 190)
            gradient.addColorStop(0, 'rgba(127, 86, 217, 1)')
            gradient.addColorStop(1, 'rgba(127, 86, 217, 0)')

            return color
          }
        : 'rgba(127, 86, 217, 1)',

    borderWidth: type !== 'line' ? 0 : 3,
  }

  return dataSet
}

const chartData = ({ labels, datasets } = {}) => {
  const data = {
    labels: labels || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: datasets || [],
  }

  return data
}

const FeedAndNonFeedChart = ({ title }) => {
  const data = useAtomValue(dashboardAtom)
  const labels = getMonthsArray()

  const currentYear = moment().year()
  const currentMonthIndex = moment().month() // Returns current month index (0 for Jan, 11 for Dec)

  // Initialize arrays with dynamic size based on the current month
  const feedPurchaseValues = Array(currentMonthIndex + 1).fill(0)
  const nonFeedPurchaseValues = Array(currentMonthIndex + 1).fill(0)

  if (data.categoryTypeSales) {
    // Loop over each categoryTypeSales object
    data.categoryTypeSales.forEach((obj) => {
      const currentYearSales = obj.yearlySales[currentYear]

      // Only process if currentYearSales exists
      if (currentYearSales) {
        currentYearSales.forEach((sale) => {
          // Use a single switch block to reduce duplication for both Feed and Non Feed
          switch (obj.categoryType) {
            case 'Feed':
              feedPurchaseValues[sale.month - 1] = sale.totalPayableAmount // Adjust month index
              break
            case 'Non Feed':
              nonFeedPurchaseValues[sale.month - 1] = sale.totalPayableAmount // Adjust month index
              break
          }
        })
      }
    })
  }

  const option = generateChartOption({ type: 'bar', title: title })
  const paymentsDataSet = generateDataSet({
    color: 'rgba(127, 86, 217, 1)',
    type: 'bar',
    label: 'Feed',
    values: feedPurchaseValues,
  })
  const purchasesDataSet = generateDataSet({
    color: 'rgba(127, 86, 217, 0.5)',
    type: 'bar',
    label: 'Non Feed',
    values: nonFeedPurchaseValues,
  })

  const paymentAndPurchaseChartData = chartData({
    labels: labels,
    datasets: [paymentsDataSet, purchasesDataSet],
  })

  return <Chart className="ml-2 pb-2" options={option} data={paymentAndPurchaseChartData} />
}

export default FeedAndNonFeedChart
