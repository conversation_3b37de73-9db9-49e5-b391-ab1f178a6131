import connectedDB from '@/app/config/database'
import Customer from '@/app/models/Customer'
import SchemeQ3 from '@/app/models/SchemeQ3'
import Users from '@/app/models/Users'

// GET /api/getSchemeQ3WithSupportPersons/:customerId
export const GET = async (request, { params }) => {
  try {
    await connectedDB()

    // Get the SchemeQ3 data
    const schemeData = await SchemeQ3.findOne({ customerId: params.customerId }, { _id: 0 }).lean()

    if (!schemeData) {
      return new Response(JSON.stringify({ error: 'No data found for this customer' }), { status: 404 })
    }

    // Get the customer details to find soId and asmId
    const customer = await Customer.findOne({ customerId: params.customerId }, { soId: 1, asmId: 1, _id: 0 }).lean()

    // Default empty result
    const result = {
      ...schemeData,
      supportPersons: [],
    }

    if (customer) {
      // Get the support persons
      const supportPersons = await Users.find(
        {
          rowId: { $in: [customer.soId, customer.asmId].filter(Boolean) },
        },
        { _id: 0 }
      ).lean()

      // Transform to desired format
      result.supportPersons = supportPersons.map((person) => ({
        profile: person.profile,
        details: { ...person },
      }))
    }

    return new Response(JSON.stringify(result), { status: 200 })
  } catch (error) {
    console.error('Error:', error)
    return new Response(JSON.stringify({ error: error.message }), { status: 500 })
  }
}
