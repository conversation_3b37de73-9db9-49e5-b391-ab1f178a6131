'use client'
import { indianCurrencyFormat } from '@/utils/formats'
import { useAtomValue } from 'jotai'
import { dashboardAtom } from './providers'

export const DueCards = () => {
  const dashboardData = useAtomValue(dashboardAtom)
  let dueValues = Array(5)
    .fill(0)
    .map((val) => val || 0)

  if (dashboardData.dues) {
    dashboardData.dues.forEach((obj) => {
      switch (obj.ageTier) {
        case '120+':
          dueValues[0] = obj.totalAmount
          break
        case '91-120':
          dueValues[1] = obj.totalAmount
          break
        case '46-90':
          dueValues[2] = obj.totalAmount
          break
        case '31-45':
          dueValues[3] = obj.totalAmount
          break
        case '0-30':
          dueValues[4] = obj.totalAmount
          break
      }
    })
  }

  const totalOverdue = indianCurrencyFormat(
    dueValues.reduce((a, b) => a + b, 0),
    0
  )

  const labels = ['120+', '91-120', '46-90', '31-45', '0-30'].filter((val, index) => dueValues[index]) // Corresponding labels
  dueValues = dueValues.filter((eachDue) => eachDue)

  return (
    <>
      <div className="mb-4 font-inter text-lg font-semibold capitalize leading-5 text-[#111827]">Dues</div>
      <div className="mb-10 flex flex-wrap gap-2 rounded-lg border border-[#E5E7EB] bg-[#F9FAFB] px-8 py-6">
        <div className="min-w-[200px] flex-1">
          <dt className="mb-2 font-inter text-sm font-normal text-[#4B5563]">TOTAL OUTSTANDING</dt>
          <dd className="font-inter text-xl font-bold text-[#111827] sm:text-2xl">{totalOverdue}</dd>
        </div>
        {eachOverdues(dueValues, labels)}
      </div>
    </>
  )
}

const eachOverdues = (dueValues, labels) => {
  return dueValues.map((val, index) => {
    if (!val) return null

    let textColorClass = 'text-red-900 font-black'
    if (index === 1) {
      textColorClass = 'text-gray-600'
    } else if (index === 2) {
      textColorClass = 'text-gray-600'
    } else if (index === 3) {
      textColorClass = 'text-gray-600'
    }

    return (
      <div key={index} className="min-w-[150px] flex-1">
        <div className="mb-2 flex items-center">
          <dt className={`font-inter text-lg text-sm font-normal text-[#4B5563] sm:text-sm ${textColorClass}`}>
            {labels[index]}
          </dt>
        </div>
        <dd className={`font-inter text-xl font-bold text-[#111827] sm:text-2xl ${textColorClass}`}>
          {indianCurrencyFormat(val, 0)}
        </dd>
      </div>
    )
  })
}
