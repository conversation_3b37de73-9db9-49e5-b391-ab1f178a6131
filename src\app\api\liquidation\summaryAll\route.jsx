import connectedDB from '@/app/config/database'
import Liquidation from '@/app/models/Liquidation'

// GET /api/liquidation/summaryAll
export const GET = async () => {
  try {
    // Connect to the database
    await connectedDB()

    // First, get the total liquidation amount for all customers
    const totalLiquidationResult = await Liquidation.aggregate([
      {
        $group: {
          _id: "$customerId",
          totalLiquidation: { $sum: "$total" }
        }
      }
    ])

    // Create a map of customer IDs to their total liquidation
    const customerTotals = new Map()
    totalLiquidationResult.forEach(item => {
      customerTotals.set(item._id, item.totalLiquidation)
    })

    // Now, get the liquidation data grouped by customer, year, and month
    const liquidationByCustomerYearMonth = await Liquidation.aggregate([
      // Make sure total is a number
      {
        $addFields: {
          total: { 
            $cond: {
              if: { $eq: [{ $type: "$total" }, "missing"] },
              then: 0,
              else: { 
                $cond: {
                  if: { $eq: [{ $type: "$total" }, "number"] },
                  then: "$total",
                  else: { $toDouble: "$total" }
                }
              }
            }
          }
        }
      },
      // Group by customerId, year, and month
      {
        $group: {
          _id: {
            customerId: "$customerId",
            year: { $toString: { $year: "$date" } },
            month: { $month: "$date" }
          },
          totalAmount: { $sum: "$total" }
        }
      },
      // Sort by customerId, year, and month
      {
        $sort: {
          "_id.customerId": 1,
          "_id.year": 1,
          "_id.month": 1
        }
      },
      // Group by customerId to prepare the year-month structure
      {
        $group: {
          _id: "$_id.customerId",
          yearMonthData: {
            $push: {
              year: "$_id.year",
              month: "$_id.month",
              amount: "$totalAmount"
            }
          }
        }
      }
    ])

    // Transform the data into the requested format
    const result = liquidationByCustomerYearMonth.map(customerData => {
      const customerId = customerData._id
      const yearMap = new Map()
      
      // Process each year-month record
      customerData.yearMonthData.forEach(item => {
        const year = item.year
        const month = item.month
        const amount = item.amount
        
        if (!yearMap.has(year)) {
          yearMap.set(year, {
            year,
            months: {}
          })
        }
        
        const yearData = yearMap.get(year)
        yearData.months[month] = amount
      })
      
      // Convert the map to an array for the final response
      const liquidationByYear = Array.from(yearMap.values())
      
      // Get the total liquidation for this customer
      const totalLiquidation = customerTotals.get(customerId) || 0
      
      // Return the formatted data for this customer
      return {
        customerId,
        liquidation: {
          totalLiquidation,
          liquidationByYear
        }
      }
    })

    return new Response(JSON.stringify({ results: result }), { status: 200 })
  } catch (error) {
    console.error("Error in liquidation summary all API:", error)
    return new Response(JSON.stringify({ error: 'Internal Server Error', details: error.message }), { status: 500 })
  }
}
