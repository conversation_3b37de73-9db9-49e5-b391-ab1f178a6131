import AccountStatementStats from '@/customComponents/accountStatementStats'

// Function to calculate the total sum of all values
const calculateTotal = (data) => {
  let total = 0

  // Iterate over each year
  Object.values(data).forEach((yearArray) => {
    yearArray.forEach((monthData) => {
      // Add the values from each month
      const value = Object.values(monthData)[0]
      total += value
    })
  })

  return total
}

const AccountStatementHeader = () => {
  return (
    <>
      <div className="mx-auto mb-5 mt-5 rounded-lg bg-white pb-7 sm:mt-0">
        {/* <Heading className="ml-8 mt-5">Transactions</Heading> */}
        <AccountStatementStats accountStatementButton={true} />
        {/* <Link
          href={'/accountStatement'}
          className="ml-6 rounded bg-white px-2 py-1 pb-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
        >
          View Account Statement
        </Link> */}
      </div>
    </>
  )
}

export default AccountStatementHeader
