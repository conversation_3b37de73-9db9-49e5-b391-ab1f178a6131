import { Schema, model, models } from 'mongoose'

const SMTrackerSchema = new Schema(
  {
    aquapartner: String,
    aquapartnerId: String,
    createdBy: String,
    createdDateTime: Date,
    date: String,
    noOfProducts: Number,
    products: [Schema.Types.ObjectId],
    asmId: String,
    rmId: String,
    rowId: String,
    soId: String,
  },
  { collection: 'SMTracker' }
)

const SMTracker = models.SMTracker || model('SMTracker', SMTrackerSchema)

export default SMTracker
