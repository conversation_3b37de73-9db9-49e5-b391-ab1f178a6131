import connectedDB from '@/app/config/database'
import AccountStatements from '@/app/models/AccountStatements'
import OpeningBalance from '@/app/models/OpeningBalance'
import { getFinancialYearStartDate } from '@/utils/date'
import moment from 'moment'
import 'moment-timezone'

// GET /api/accountStatements/:id
export const GET = async (request, { params }) => {
  // Always use Asia/Kolkata timezone for consistency
  const today = moment().tz('Asia/Kolkata')
  const currentMonth = today.month() // Get the current month (0-11)

  const financialStartDate =
    currentMonth >= 3
      ? moment.tz('Asia/Kolkata').startOf('year').month(3).date(1)
      : moment.tz('Asia/Kolkata').subtract(1, 'year').startOf('year').month(3).date(1)

  try {
    // Use the timezone-aware version for consistency
    const financialYearStartDate = moment.tz(getFinancialYearStartDate(), 'Asia/Kolkata')
    const financialYearStartDateFormatted = financialYearStartDate.format('DD-MMM-YYYY')

    const openingBalanceEntry = {
      txnDate: financialYearStartDate.toISOString(),
      vchType: 'Opening Balance',
      invoiceNumber: '',
      particulars: '***Opening Balance***',
      debit: 0,
      credit: 0,
      total: 0,
      balance: 0,
    }

    await connectedDB()

    // Use cloned moment objects to prevent mutation issues
    const fyStartDate = financialStartDate.clone()
    const fyEndDate = financialStartDate.clone().add(1, 'year').month(2).endOf('month')

    // Perform sorting before grouping to ensure correct order
    let accountStatements = await AccountStatements.aggregate([
      {
        $match: {
          customerId: params.id,
          txnDate: {
            $gte: fyStartDate.toDate(),
            $lte: fyEndDate.toDate(),
          },
        },
      },
      {
        $group: {
          _id: {
            invoiceNumber: '$invoiceNumber',
            txnDate: '$txnDate',
          },
          total: { $sum: '$total' },
          debit: { $sum: '$debit' },
          credit: { $sum: '$credit' },
          tcs: { $first: '$tcs' },
          particulars: { $push: '$particulars' },
          vchType: { $first: '$vchType' },
        },
      },
      {
        $project: {
          _id: 0,
          invoiceNumber: '$_id.invoiceNumber',
          txnDate: '$_id.txnDate',
          total: 1,
          debit: 1,
          tcs: 1,
          credit: 1,
          vchType: 1,
          particulars: {
            $reduce: {
              input: '$particulars',
              initialValue: '',
              in: {
                $concat: [
                  '$$value',
                  {
                    $cond: {
                      if: { $eq: ['$$value', ''] },
                      then: '',
                      else: ', ',
                    },
                  },
                  '$$this',
                ],
              },
            },
          },
        },
      },
      {
        $sort: {
          txnDate: 1,
        },
      },
    ])

    let balance = 0

    // Query for opening balance with exact date string match
    const customerOpeningBalance = await OpeningBalance.findOne({
      customerId: params.id,
      date: financialYearStartDateFormatted,
    })

    if (customerOpeningBalance) {
      balance = customerOpeningBalance.openingBalance
    }

    openingBalanceEntry.balance = balance

    // Calculate running balance with rounding
    accountStatements = accountStatements.map((transaction) => {
      transaction.total = Math.round(transaction.total)
      transaction.debit = Math.round(transaction.debit)
      transaction.credit = Math.round(transaction.credit)

      if (transaction.credit > 0) {
        transaction.total = 0
      }

      if (transaction.tcs > 0) {
        transaction.total += transaction.tcs
      }

      if (transaction.vchType === 'Journal') {
        transaction.credit = transaction.total
        transaction.total = 0
      }

      balance += transaction.total - transaction.credit
      transaction.balance = Math.round(balance)

      return transaction
    })

    if (!accountStatements || accountStatements.length === 0) {
      return new Response(JSON.stringify({ results: [] }), { status: 200 })
    } else {
      accountStatements = [openingBalanceEntry, ...accountStatements]
      return new Response(JSON.stringify({ results: accountStatements }), { status: 200 })
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal Server Error', details: error.message }), { status: 500 })
  }
}
