import zohoPaymentService from '@/app/lib/zohoPaymentService'

// POST /api/initiatePayment
// Legacy endpoint - redirects to new Zoho Payment API
export const POST = async (req) => {
  try {
    const body = await req.json()

    // Extract fields from the body
    const { amount, invoiceNo, redirectUrl, referenceId, customerId, customerName, customerEmail, customerPhone } = body

    // Validate required fields
    if (!amount || !invoiceNo) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields',
          message: 'amount and invoiceNo are required',
          required_fields: ['amount', 'invoiceNo'],
        }),
        { status: 400 }
      )
    }

    // Convert amount to number
    const parsedAmount = parseFloat(amount)
    if (isNaN(parsedAmount) || parsedAmount <= 0) {
      return new Response(
        JSON.stringify({
          error: 'Invalid amount',
          message: 'Amount must be a positive number',
        }),
        { status: 400 }
      )
    }

    console.log(`[LEGACY] Initiating payment for invoice: ${invoiceNo}, amount: ${parsedAmount}`)

    // Prepare payment data for new service
    const paymentData = {
      amount: parsedAmount,
      currency: 'INR',
      description: `Payment for Invoice ${invoiceNo}`,
      invoice_number: invoiceNo,
      customer_id: customerId || 'LEGACY_CUSTOMER',
      customer_name: customerName,
      customer_email: customerEmail,
      customer_phone: customerPhone,
      redirect_url: redirectUrl,
      reference_id: referenceId,
      meta_data: [
        { key: 'legacy_endpoint', value: 'true' },
        { key: 'invoice_no', value: invoiceNo },
      ],
    }

    // Use new Zoho Payment service
    const result = await zohoPaymentService.createPaymentSession(paymentData)

    // Return response in legacy format for backward compatibility
    return new Response(
      JSON.stringify({
        result: 'success',
        paymentSession: result.payment_session,
        // Additional data for enhanced functionality
        transaction_id: result.transaction_id,
        payment_session_id: result.payment_session.payments_session_id,
        expires_in: '15 minutes',
        // Legacy compatibility
        paymentSessionId: result.payment_session.payments_session_id,
      }),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error in legacy initiatePayment API:', error)

    // Handle specific errors
    if (error.message.includes('token')) {
      return new Response(
        JSON.stringify({
          error: 'Authentication Error',
          message: 'Zoho Payment authentication failed. Please check configuration.',
          details: error.message,
        }),
        { status: 401 }
      )
    }

    if (error.message.includes('Zoho API Error')) {
      return new Response(
        JSON.stringify({
          error: 'Zoho Payment API Error',
          message: error.message,
          details: 'Please check your payment data and try again',
        }),
        { status: 400 }
      )
    }

    return new Response(
      JSON.stringify({
        error: 'Payment initiation failed',
        message: error.message,
        details: 'An unexpected error occurred while initiating payment',
      }),
      { status: 500 }
    )
  }
}
