import React from 'react';
import { Divider } from '@/components/divider';
import { Badge } from '@/components/badge';

const Stat = ({ title, value, change }) => {
  return (
    <div>
      <Divider />
      <div className="mt-6 text-lg/6 font-medium sm:text-sm/6">{title}</div>
      <div className="mt-3 text-3xl/8 font-semibold sm:text-2xl/8">{value}</div>
      <div className="mt-3 text-sm/6 sm:text-xs/6">
        <Badge color={change && change.startsWith('+') ? 'lime' : 'pink'}>
          {change}
        </Badge>{' '}
        {change && <span className="text-zinc-500">from last month</span>}
      </div>
    </div>
  );
};

export default Stat;
