import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import { <PERSON>alog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { SetStateAction } from 'jotai'
import { Dispatch } from 'react'

export function BrandsModal({
  open,
  setOpen,
  brandsList,
}: {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  brandsList: string[]
}) {
  return (
    <>
      <Dialog open={open} onClose={setOpen} className="relative z-10">
        <DialogBackdrop
          transition
          className="fixed inset-0 hidden bg-gray-200 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:block"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-stretch justify-center text-center md:items-center md:px-2 lg:px-4">
            {/* This element is to trick the browser into centering the modal contents. */}
            <span aria-hidden="true" className="hidden md:inline-block md:h-screen md:align-middle">
              &#8203;
            </span>
            <DialogPanel
              transition
              className="flex w-full transform text-left text-base transition data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:my-8 md:max-w-2xl md:px-4 data-[closed]:md:translate-y-0 data-[closed]:md:scale-95 lg:max-w-4xl"
            >
              <div className="relative flex w-full flex-col items-start justify-start overflow-hidden bg-white px-4 pb-8 pt-14 text-left shadow-2xl sm:px-6 sm:pt-8 md:p-6 lg:p-8">
                <button
                  type="button"
                  onClick={() => setOpen(false)}
                  className="absolute right-4 top-4 text-gray-400 hover:text-gray-500 sm:right-6 sm:top-8 md:right-6 md:top-6 lg:right-8 lg:top-8"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon aria-hidden="true" className="h-6 w-6" />
                </button>

                <div className="mx-auto max-w-2xl justify-start px-4 pb-16 pt-10 sm:px-6">
                  <div className="lg:col-span-2 lg:border-gray-200 lg:pr-8">
                    <h1 className="mb-5 text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">
                      Eligible Brands and Discounts
                    </h1>
                  </div>

                  <Table className="mt-4 w-full [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
                    <TableHead>
                      <TableRow>
                        <TableHeader>Discounts upto 50% on</TableHeader>

                        {/* <TableHeader>Discount</TableHeader> */}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {brandsList.map((brand, index) => {
                        const [name, percentage, type] = brand.split('$')
                        const [productName, sku] = name.split('*')
                        return (
                          <TableRow key={index} href={''} title={`brand #${brand}`}>
                            <TableCell>
                              <div className={`flex flex-col ${type.trim() == 'brand' ? 'font-bold' : 'font-normal'}`}>
                                <span>{productName}</span>
                                <span className="font-medium">{sku}</span>
                                {/* <TableCell>{percentage}</TableCell> */}
                              </div>
                            </TableCell>
                            {/*                             <TableCell>{percentage}</TableCell>
                             */}{' '}
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                  <div className="border-1 rounded-sm border-gray-300 bg-yellow-100 p-2">
                    Note: Discounts upto 45% are available for other healthcare products
                  </div>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  )
}
