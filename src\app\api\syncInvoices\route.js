import connectedDB from '@/app/config/database'
import InvoicesPayload from '@/app/models/InvoiceSync'

// POST /api/syncInvoices
export const POST = async (request, { params }) => {
  try {
    // Connect to the database
    await connectedDB()

    // Parse the JSON body
    const invoicesPayload = new InvoicesPayload(await request.json())
    await invoicesPayload.save()

    // Log the parsed body to verify

    // If you want to fetch invoices based on customerId from params
    const invoices = await InvoicesPayload.find({}).limit(10)

    // Respond with success
    return new Response(JSON.stringify({ message: 'Data received successfully' }), { status: 201 })
  } catch (error) {
    console.error(error)
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    })
  }
}
