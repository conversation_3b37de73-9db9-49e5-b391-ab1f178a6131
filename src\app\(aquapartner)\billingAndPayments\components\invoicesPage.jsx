'use client'

import { Badge } from '@/components/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { indianCurrencyFormat } from '@/utils/formats'
import { useFetchInvoicesByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { InvoiceDetails } from './invoiceDetails'
// Utility function for class names
function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

export const InvoicesPage = () => {
  const [loading, setLoading] = useState(true)
  const customer = useAtomValue(retailerAtom)
  const [invoice, setInvoice] = useState({})
  const [isPopupOpen, setIsPopupOpen] = useState(false)

  const invoices = useFetchInvoicesByCustomerId(customer.customerId)

  const handleOpenPopup = ({ selectedInvoice }) => {
    setInvoice((prevVal) => selectedInvoice)
    setIsPopupOpen(true)
  }

  /* const initiatePayment = async ({ selectedInvoice }) => {
    try {
      const response = await fetch('/api/initiatePayment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }, // invoiceNo: selectedInvoice?.invoiceNumber
        body: JSON.stringify({ amount: selectedInvoice?.total, invoiceNo: selectedInvoice?.invoiceNumber }),
      })
      const data = await response.json()
      if (data.error) {
        alert('Error, Try again after some time')
        return
      }
      let config = {
        account_id: '***********',
        domain: 'IN',
        otherOptions: {
          api_key: '1003.f35b9411653295bb03db1e8490dc6cdd.0f625d54ec97f4eba7bedd0dc6fc23b8',
        },
      }
      let instance = new window.ZPayments(config)
      try {
        let options = {
          amount: data.paymentSession.amount,
          currency_code: 'INR',
          payments_session_id: data.paymentSession.payments_session_id,
          currency_symbol: '₹',
          business: customer?.companyName,
          description: '',
          address: {
            name: customer?.customerName,
            email: customer?.Email ?? '<EMAIL>',
          },
        }
        let paymentResponse = await instance.requestPaymentMethod(options)
      } catch (error) {
        console.log(JSON.stringify(error))
        if (err.code != 'widget_closed') {
          console.error('Widget Closed')
        }
      } finally {
        await instance.close()
      }
    } catch (error) {
      console.log('Payment Error : ', error)
    }
  }
 */
  const handleClosePopup = () => {
    setIsPopupOpen(false)
  }

  useEffect(() => {
    if (invoices?.results) {
      setLoading((prev) => false)
    }
  }, [invoices])

  /* useEffect(() => {
    // Load Zoho Payments widget dynamically
    const script = document.createElement('script')
    script.src = 'https://static.zohocdn.com/zpay/zpay-js/v1/zpayments.js'
    script.async = true
    document.body.appendChild(script)
    return () => {
      document.body.removeChild(script)
    }
  }, []) */

  return (
    <>
      {loading && <LoadingScreen />}
      <Table className="mt-2 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
        <TableHead>
          <TableRow>
            <TableHeader>Invoice date</TableHeader>
            <TableHeader>Invoice Number</TableHeader>
            <TableHeader className="text-right">Total</TableHeader>
            <TableHeader>Status</TableHeader>
            <TableHeader></TableHeader>
          </TableRow>
        </TableHead>
        <TableBody>
          {invoices.results &&
            invoices.results.map((invoice, index) => (
              <TableRow className="hover:bg-blue-50" key={index} title={`Order # No`}>
                <TableCell className="" onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {moment(invoice.invoiceDate).format('DD-MM-YYYY')}
                </TableCell>
                <TableCell onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {invoice.invoiceNumber}
                </TableCell>
                <TableCell className="text-right" onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  {indianCurrencyFormat(invoice.total)}
                </TableCell>

                <TableCell onClick={() => handleOpenPopup({ selectedInvoice: invoice })}>
                  <Badge
                    color={
                      invoice.invoiceStatus === 'Void'
                        ? 'zinc'
                        : invoice.invoiceStatus === 'Closed'
                          ? 'green'
                          : invoice.invoiceStatus === 'Invoiced'
                            ? 'blue'
                            : invoice.invoiceStatus === 'Partially Delivered'
                              ? 'yellow'
                              : invoice.invoiceStatus === 'Rejected'
                                ? 'red'
                                : 'yellow'
                    }
                  >
                    {invoice.invoiceStatus}
                  </Badge>
                </TableCell>
                {/* <TableCell>
                  <Link
                    href="#"
                    onClick={() => initiatePayment({ selectedInvoice: invoice })}
                    className="text-blue-700"
                  >
                    PayNow
                  </Link>
                </TableCell> */}
              </TableRow>
            ))}
        </TableBody>
      </Table>

      {isPopupOpen && (
        <InvoiceDetails customerInvoice={invoice} open={isPopupOpen} setOpen={setIsPopupOpen} customer={customer} />
      )}
    </>
  )
}
