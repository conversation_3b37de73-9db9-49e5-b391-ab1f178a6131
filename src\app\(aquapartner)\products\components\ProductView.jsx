/*
  This example requires some changes to your config:
  
  ```
  // tailwind.config.js
  module.exports = {
    // ...
    theme: {
      extend: {
        gridTemplateRows: {
          '[auto,auto,1fr]': 'auto auto 1fr',
        },
      },
    },
    plugins: [
      // ...
      require('@tailwindcss/aspect-ratio'),
    ],
  }
  ```
*/
'use client'

import { retailerAtom } from '@/customComponents/providers'
import { useAtomValue } from 'jotai'
import { useSnackbar } from '../../../context/SnackbarContext'



export default function NewProductDetailsView({ productId, productImage, productName, content }) {
  const { showSnackBar } = useSnackbar()
  const customer = useAtomValue(retailerAtom)

  const handleInterestClick = (productId, productName) => {
    const requestBody = JSON.stringify({
      customerId: customer.customerId,
      name: customer.companyName,
      mobile: customer.mobileNumber,
      productId,
      productName
      
    })
     fetch('/api/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: requestBody,
    })
      .then((res) => res.json())
      .then((res) => showSnackBar('Your dedicated AquaConnect officer will reach out to you with details.', 'success'))
      .catch((err) => {
        console.log(err.message)
        showSnackBar(err.message, 'error')
      }) 
    // const randomBinary = Math.round(Math.random())
    // randomBinary
    //   ? showSnackBar('Your dedicated AquaConnect officer will reach out to you with details.', 'success')
    //   : showSnackBar('Your inquiry has already been submitted', 'error')
  }

  return (
    <div className="bg-white">
      <div className="pt-6">
        {/* Image gallery */}
        <div className="mx-auto mt-4 max-w-2xl sm:px-6 lg:grid lg:max-w-7xl lg:grid-cols-3 lg:gap-x-8 lg:px-8">
          <div className="aspect-h-4 aspect-w-3 hidden overflow-hidden rounded-lg lg:block"></div>
          <div className="hidden lg:grid lg:grid-cols-1 lg:gap-y-8">
            <div className="aspect-h-2 aspect-w-6 overflow-hidden rounded-lg">
              <img alt="productImage" src={productImage} className="h-full w-full object-cover object-center" />
            </div>
          </div>
          <div className="aspect-h-5 aspect-w-4 lg:aspect-h-4 lg:aspect-w-6 sm:overflow-hidden sm:rounded-lg lg:hidden">
            <img alt="productImage" src={productImage} className="object-fit h-full w-full object-center" />
          </div>
        </div>

        {/* Product info */}
        <div className="mx-auto max-w-2xl px-4 pb-16 pt-10 sm:px-6">
          <div className="lg:col-span-2 lg:border-gray-200 lg:pr-8">
            <h1 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">{productName}</h1>
          </div>

          {/* Options */}

          {/*   <div className="mt-4 lg:row-span-3 lg:mt-0">
            <h2 className="sr-only">Product information</h2>
            <p className="text-3xl tracking-tight text-gray-900">{product.price}</p>

          </div> */}

          <div className="lg:col-span-2 lg:col-start-1 lg:border-gray-200">
            <div
              className="[&_h2]:mb-2 [&_h2]:mt-6 [&_h2]:font-bold [&_h2]:text-blue-600 [&_ul_li]:list-none"
              dangerouslySetInnerHTML={{ __html: content || '' }}
            />
          </div>
          <button
            type="button"
            onClick={() => handleInterestClick(productId, productName)}
            className="mt-10 flex w-full items-center justify-center rounded-md border border-transparent bg-indigo-600 px-8 py-3 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            {"I'm interested"}
          </button>

          {/* {showMessage && (
            <div className="mt-4 text-center font-medium text-green-600">
              {'Your dedicated AquaConnect officer will reach out to you with details.'}
            </div>
          )} */}
        </div>
      </div>
    </div>
  )
}
