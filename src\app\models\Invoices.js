import { Schema, model, models } from 'mongoose'

const InvoiceSchema = new Schema(
  {
    invoiceId: String,
    invoiceNumber: String,
    invoiceStatus: String,
    addressId: String,
    customerId: String,
    ageInDays: Number,
    ageTier: String,
    subTotal: Number,
    total: Number,
    balance: Number,
    invoiceDate: Date,
    deliveryMode: String,
    deliveryStatus: String,
    dueDate: Date,
  },
  { collection: 'Invoices' }
)

const Invoices = models.Invoices || model('Invoices', InvoiceSchema)

export default Invoices
