export const PriceCard = ({ title, date, isSharable, priceData }) => {
  return (
    <div className="mt-2 flex-row items-start justify-start rounded-lg">
      {/* Header */}
      <div className="flex items-start justify-between border-l border-r border-t border-gray-300 p-2">
        <div className="flex flex-row items-start">
          <div className="flex items-center">
            <img
              src="/images/shrimp-icon.svg" // Replace with your icon URL
              alt="Shrimp Icon"
              className="mr-2 h-6 w-6"
            />
            <h2 className="text-sm font-semibold text-gray-800">{title}</h2>
          </div>
          <span className="justify-center self-center pl-3 text-center text-[12px] font-semibold text-[#949494]">
            {date}
          </span>
        </div>
        {isSharable && (
          <button className="flex text-sm text-blue-600 hover:underline">
            <img
              src="/images/share-icon.svg" // Replace with your icon URL
              alt="Share Icon"
              className="mr-2 h-6 w-6"
            />
            Share
          </button>
        )}
      </div>

      {/* Table with Hidden Scrollbar */}
      <div
        className="overflow-x-auto"
        style={{
          scrollbarWidth: 'none', // For Firefox
          msOverflowStyle: 'none', // For IE/Edge
        }}
      >
        <style>
          {`
            /* For Chrome, Edge, and Safari */
            .scroll-hidden::-webkit-scrollbar {
              display: none;
            }
          `}
        </style>
        <table className="scroll-hidden w-full table-auto border-collapse border border-gray-300 no-underline">
          <tbody className="">
            {['count', 'price'].map((label, labelIndex) => (
              <tr key={labelIndex} className="border-b border-gray-300">
                <td className="overflow-hidden text-ellipsis whitespace-nowrap border-l-0 border-r border-gray-300 px-2 py-1 text-sm text-[#616161]">
                  {label === 'count' ? 'Count' : 'Price (₹)'}
                </td>
                {priceData.map((row, index) => (
                  <td key={`${label}-${index}`} className="border-r border-gray-300 px-2 py-1 text-center ">
                    {label === 'count' ? row.count : row.price}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
