import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * GET /api/zoho/payments/status/[sessionId]
 * Get payment session status and details
 */
export async function GET(request, { params }) {
  try {
    const { sessionId } = params

    if (!sessionId) {
      return new Response(
        JSON.stringify({
          error: 'Missing session ID',
          message: 'Payment session ID is required',
        }),
        { status: 400 }
      )
    }

    // Get payment session from Zoho
    const paymentSession = await zohoPaymentService.getPaymentSession(sessionId)

    // Get local transaction record
    const transaction = await zohoPaymentService.getTransaction(sessionId)

    // Update local transaction if payment status has changed
    if (transaction && paymentSession.status !== transaction.status) {
      const statusData = {
        status: paymentSession.status,
      }

      // If payment is completed, get payment details
      if (paymentSession.payments && paymentSession.payments.length > 0) {
        const latestPayment = paymentSession.payments[paymentSession.payments.length - 1]
        statusData.payment_id = latestPayment.payment_id
        statusData.payment_method = latestPayment.payment_method
      }

      await zohoPaymentService.updateTransactionStatus(sessionId, statusData)
    }

    const response = {
      success: true,
      message: 'Payment status retrieved successfully',
      data: {
        payment_session_id: paymentSession.payments_session_id,
        status: paymentSession.status,
        amount: paymentSession.amount,
        currency: paymentSession.currency,
        description: paymentSession.description,
        invoice_number: paymentSession.invoice_number,
        created_time: paymentSession.created_time,
        payments: paymentSession.payments || [],
        meta_data: paymentSession.meta_data || [],
      },
    }

    // Add transaction details if available
    if (transaction) {
      response.data.transaction = {
        id: transaction._id,
        customer_id: transaction.customer_id,
        customer_name: transaction.customer_name,
        reference_id: transaction.reference_id,
        created_at: transaction.createdAt,
        updated_at: transaction.updatedAt,
      }
    }

    return new Response(JSON.stringify(response), { status: 200 })
  } catch (error) {
    console.error('Error retrieving payment status:', error.message)

    // Handle specific errors
    if (error.message.includes('Zoho API Error')) {
      return new Response(
        JSON.stringify({
          error: 'Zoho Payment API Error',
          message: error.message,
          details: 'Payment session may not exist or may have expired',
        }),
        { status: 404 }
      )
    }

    if (error.message.includes('token')) {
      return new Response(
        JSON.stringify({
          error: 'Authentication Error',
          message: error.message,
          details: 'Please check your Zoho Payment configuration',
        }),
        { status: 401 }
      )
    }

    return new Response(
      JSON.stringify({
        error: 'Status retrieval failed',
        message: error.message,
        details: 'An unexpected error occurred while retrieving payment status',
      }),
      { status: 500 }
    )
  }
}

/**
 * PUT /api/zoho/payments/status/[sessionId]
 * Manually update payment status (for webhook simulation or manual updates)
 */
export async function PUT(request, { params }) {
  try {
    const { sessionId } = params
    const body = await request.json()

    if (!sessionId) {
      return new Response(
        JSON.stringify({
          error: 'Missing session ID',
          message: 'Payment session ID is required',
        }),
        { status: 400 }
      )
    }

    const { status, payment_id, payment_method, error_code, error_message } = body

    if (!status) {
      return new Response(
        JSON.stringify({
          error: 'Missing status',
          message: 'Payment status is required',
        }),
        { status: 400 }
      )
    }

    // Validate status
    const validStatuses = ['created', 'pending', 'succeeded', 'failed', 'cancelled', 'expired']
    if (!validStatuses.includes(status)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid status',
          message: `Status must be one of: ${validStatuses.join(', ')}`,
        }),
        { status: 400 }
      )
    }

    const statusData = {
      status,
      payment_id,
      payment_method,
      error_code,
      error_message,
    }

    const transaction = await zohoPaymentService.updateTransactionStatus(sessionId, statusData)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Payment status updated successfully',
        data: {
          transaction_id: transaction._id,
          payment_session_id: transaction.payments_session_id,
          status: transaction.status,
          updated_at: transaction.updatedAt,
        },
      }),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error updating payment status:', error.message)

    if (error.message.includes('Transaction not found')) {
      return new Response(
        JSON.stringify({
          error: 'Transaction not found',
          message: 'No transaction found with the provided session ID',
        }),
        { status: 404 }
      )
    }

    return new Response(
      JSON.stringify({
        error: 'Status update failed',
        message: error.message,
        details: 'An unexpected error occurred while updating payment status',
      }),
      { status: 500 }
    )
  }
}
