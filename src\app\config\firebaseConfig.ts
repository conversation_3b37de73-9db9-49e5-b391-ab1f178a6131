// Import the functions you need from the SDKs you need

import { errorAtom, firebaseUser<PERSON>tom, loadingAtom, retailerAtom } from '@/customComponents/providers'
import { clarity } from '@/utils/clarity'
import { validateRetailer } from '@/utils/requests'
import { initializeApp } from 'firebase/app'
import { getAuth, onAuthStateChanged } from 'firebase/auth'
import { useAtom } from 'jotai'
import { useEffect } from 'react'

const firebaseConfig = {
  apiKey: 'AIzaSyAMVVgOXZJGk1rTl7sLr2RKM7S27O37XFo',
  authDomain: 'aquaconnect-partner-prod.firebaseapp.com',
  projectId: 'aquaconnect-partner-prod',
  storageBucket: 'aquaconnect-partner-prod.appspot.com',
  messagingSenderId: '296786761398',
  appId: '1:296786761398:web:7696eca246681b7455f817',
  measurementId: 'G-JJCX2MN5LK',
}

const app = initializeApp(firebaseConfig)
const auth = getAuth(app)

// Fetch farmers  Visits by retailer customerId
function useAuth() {
  const [user, setUser] = useAtom(firebaseUserAtom)
  const [loading, setLoading] = useAtom(loadingAtom)
  const [retailer, setRetailer] = useAtom(retailerAtom)
  const [error, setError] = useAtom(errorAtom)

  // Handle user and retailer state
  useEffect(() => {
    const unSubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser?.phoneNumber) {
        validateRetailer(firebaseUser.phoneNumber).then((retailer) => {
          if (retailer.result) {
            setRetailer(retailer.result)
            setUser(firebaseUser)
            clarity('', '', retailer.result.customerCode)
            setLoading((prev) => false)
            setError((prev) => null)
          } else {
            setRetailer((prev) => ({
              customerId: '',
              customerCode: '',
              customerName: '',
              contactName: '',
              GSTNo: '',
              companyName: '',
              mobileNumber: '',
              Email: '',
              billingAddress: '',
              businessVertical: '',
              customerType: '',
              Status: '',
              rowId: '',
            }))
            //setError(firebaseUser.phoneNumber + ' - Invalid Retailer')
            setError((prev) => ({
              ['message']: `${firebaseUser.phoneNumber} is not registered with us. No worries! Please call us at +************, and we'll help you create an account.`, // Add or update the specific error
            }))

            auth.signOut()

            setLoading((prev) => false)
          }
        })
      } else {
        setLoading((prev) => false)
      }

      setUser(firebaseUser)
    })

    return () => {
      unSubscribe()
    }
  }, [setLoading, setUser, setRetailer, setError])

  return user
}

export { auth, useAuth }
