import connectedDB from '@/app/config/database'
import Liquidation from '@/app/models/Liquidation'

// GET /api/liquidation/summaryAllOptimized
export const GET = async () => {
  try {
    // Connect to the database
    await connectedDB()

    // Single optimized aggregation pipeline
    const liquidationSummary = await Liquidation.aggregate([
      // Stage 1: Ensure total is a number and extract year/month
      {
        $addFields: {
          // Handle missing, null, or non-numeric total values
          total: {
            $cond: {
              if: { $eq: [{ $type: "$total" }, "missing"] },
              then: 0,
              else: {
                $cond: {
                  if: { $eq: [{ $type: "$total" }, "number"] },
                  then: "$total",
                  else: { $toDouble: { $ifNull: ["$total", 0] } }
                }
              }
            }
          },
          // Extract and format year and month
          year: { $toString: { $year: "$date" } },
          month: { $month: "$date" }
        }
      },

      // Stage 2: Group by customerId, year, and month to get monthly totals
      // This reduces the data volume early in the pipeline
      {
        $group: {
          _id: {
            customerId: "$customerId",
            year: "$year",
            month: "$month"
          },
          monthlyTotal: { $sum: "$total" }
        }
      },

      // Stage 3: Sort early to optimize memory usage
      {
        $sort: {
          "_id.customerId": 1,
          "_id.year": 1,
          "_id.month": 1
        }
      },

      // Stage 4: Group by customerId and year to create the months object
      {
        $group: {
          _id: {
            customerId: "$_id.customerId",
            year: "$_id.year"
          },
          // Create key-value pairs for months
          months: {
            $push: {
              k: { $toString: "$_id.month" },
              v: "$monthlyTotal"
            }
          },
          // Calculate year total for later use
          yearTotal: { $sum: "$monthlyTotal" }
        }
      },

      // Stage 5: Convert the months array to an object and prepare for next stage
      {
        $project: {
          customerId: "$_id.customerId",
          yearData: {
            year: "$_id.year",
            months: { $arrayToObject: "$months" }
          },
          yearTotal: 1
        }
      },

      // Stage 6: Group by customerId to create the liquidationByYear array and calculate total
      {
        $group: {
          _id: "$customerId",
          totalLiquidation: { $sum: "$yearTotal" },
          liquidationByYear: {
            $push: "$yearData"
          }
        }
      },

      // Stage 7: Final formatting with proper structure
      {
        $project: {
          _id: 0,
          customerId: "$_id",
          liquidation: {
            totalLiquidation: "$totalLiquidation",
            liquidationByYear: "$liquidationByYear"
          }
        }
      },

      // Stage 8: Sort by customerId for consistent results
      {
        $sort: {
          "customerId": 1
        }
      }
    ])

    return new Response(JSON.stringify({ results: liquidationSummary }), { status: 200 })
  } catch (error) {
    console.error("Error in liquidation summary all optimized API:", error)
    return new Response(JSON.stringify({
      error: 'Internal Server Error',
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }), { status: 500 })
  }
}
