import { Schema, model, models } from 'mongoose'

const SalesSchema = new Schema({
  year: String,
  months: [Object],
})

// Define the Main Schema
const liquidationByRetailerSchema = new Schema(
  {
    partnerId: String,
    latestCreatedDateTime: String,
    TotalSales: Number,
    sales: [SalesSchema], // Embed the Product Schema
  },
  { collection: 'liquidationByRetailer' }
)

const LiquidationByRetailer =
  models.liquidationByRetailer || model('liquidationByRetailer', liquidationByRetailerSchema)

export default LiquidationByRetailer
