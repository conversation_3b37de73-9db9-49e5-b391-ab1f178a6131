import { Schema, model, models } from 'mongoose'

const SchemeAquaPartnersSchema = new Schema(
  {
    retailerId: String,
    retailerName: String,
    soName: String,
    asmName: String,
    soProfile: String,
    mobile: String,
  },
  { collection: 'SchemeAquaPartnersQ2' }
)

const SchemeAquaPartners = models.SchemeAquaPartnersQ2 || model('SchemeAquaPartnersQ2', SchemeAquaPartnersSchema)
export default SchemeAquaPartners
