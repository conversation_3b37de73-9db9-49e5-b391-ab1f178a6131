# Products API

## Overview

The Products API provides access to the complete list of products available in the system.

## Endpoint

```
GET /api/products
```

## Parameters

This endpoint does not require any parameters.

## Response

### Success Response

**Status Code:** 200 OK

**Response Body:**
```json
[
  {
    "productId": "PROD001",
    "productName": "Aqua Feed - Growth Formula",
    "description": "High-quality feed for optimal growth of aquatic species",
    "category": "Feed",
    "price": 2000,
    "status": "Active"
  },
  {
    "productId": "PROD002",
    "productName": "Aqua Chemicals - Water Treatment",
    "description": "Chemicals for maintaining optimal water quality",
    "category": "Chemicals",
    "price": 5000,
    "status": "Active"
  }
]
```

### Error Response

**Status Code:** 500 Internal Server Error

**Response Body:**
```json
{
  "error": "Internal Server Error"
}
```

## Implementation Details

The API performs the following operations:

1. Connects to the database
2. Retrieves all products from the Products collection
3. Returns the products as a JSON array

## Data Models

The API uses the following MongoDB model:

### Products

Contains product records with the following fields:
- `productId`: Unique identifier for the product
- `productName`: The name of the product
- `description`: Detailed description of the product
- `category`: The category of the product
- `price`: The price of the product
- `status`: The current status of the product (e.g., "Active", "Inactive")

## Example Usage

### Request

```
GET /api/products
```

### Response

```json
[
  {
    "productId": "PROD001",
    "productName": "Aqua Feed - Growth Formula",
    "description": "High-quality feed for optimal growth of aquatic species",
    "category": "Feed",
    "price": 2000,
    "status": "Active"
  },
  {
    "productId": "PROD002",
    "productName": "Aqua Chemicals - Water Treatment",
    "description": "Chemicals for maintaining optimal water quality",
    "category": "Chemicals",
    "price": 5000,
    "status": "Active"
  },
  {
    "productId": "PROD003",
    "productName": "Aqua Equipment - Aerator",
    "description": "Equipment for maintaining optimal oxygen levels",
    "category": "Equipment",
    "price": 15000,
    "status": "Active"
  }
]
```

## Code Implementation

The implementation uses Next.js API routes with MongoDB for data storage. The API connects to the database and retrieves all products from the Products collection.

Key features of the implementation:
- Simple database query to retrieve all products
- Returns an empty array if no products are found
- Returns a 500 error if an exception occurs during processing

## POST Endpoint for Customer Interest

The Products API also includes a POST endpoint for recording customer interest in products.

```
POST /api/products
```

### Request Body

```json
{
  "customerId": "CUST001",
  "productId": "PROD001",
  "interestLevel": "High",
  "notes": "Customer is interested in bulk purchase"
}
```

### Success Response

**Status Code:** 200 OK

**Response Body:**
```json
{
  "result": "Success"
}
```

### Error Response

**Status Code:** 500 Internal Server Error

**Response Body:**
```json
{
  "error": "error",
  "message": "try again."
}
```

### Implementation Details

The POST endpoint performs the following operations:

1. Connects to the database
2. Parses the request body
3. Adds timestamp and source information
4. Inserts the customer interest record into the CustomerInterest collection
5. Returns a success response

### Data Models

The POST endpoint uses the following MongoDB model:

### CustomerInterest

Contains customer interest records with the following fields:
- `customerId`: The ID of the customer
- `productId`: The ID of the product
- `interestLevel`: The level of interest (e.g., "High", "Medium", "Low")
- `notes`: Additional notes about the customer's interest
- `datetime`: The timestamp when the interest was recorded
- `source`: The source of the interest record (e.g., "PartnerApp")
