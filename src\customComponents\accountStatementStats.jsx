import { Button } from '@/customComponents/button'
import { Stat } from '@/customComponents/common/stat'
import { indianCurrencyFormat } from '@/utils/formats'
import { useAtomValue } from 'jotai'
import _ from 'lodash'
import Link from 'next/link'
import { dashboardAtom } from './providers'

// Function to calculate the total sum of all values
const calculateTotal = (data, types, key) => {
  let total = 0

  if (types == 'Sales') {
    console.log('Dashboard Type : ', Object.values(data))
  }
  // Iterate over each year
  Object.values(data).forEach((yearArray) => {
    yearArray.forEach((monthData) => {
      // Add the values from each month
      const value = Object.values(monthData)[0][key]
      total += value
    })
  })

  return total
}

const AccountStatementStats = ({ accountStatementButton = false }) => {
  const data = useAtomValue(dashboardAtom)

  var [sales, outstanding, paid, due, liquidation] = [0, 0, 0, 0, 0]

  if (data.sales) {
    sales = calculateTotal(data.sales, 'Sales', 'totalSales')
  }

  if (data.payments) {
    paid = calculateTotal(data.payments, 'Payments', 'totalPayment')
  }
  if (data.dues) {
    outstanding = _.reduce(data.dues, (sum, item) => sum + item.totalAmount, 0)
    const filteredData = _.filter(
      data.dues,
      (item) =>
        item.ageTier === '31-45' || item.ageTier === '46-90' || item.ageTier === '91-120' || item.ageTier === '120+'
    )
    due = _.sumBy(filteredData, 'totalAmount')
  }

  if (data.liquidation) {
    liquidation = data.liquidation.totalLiquidation
  }

  const stats = [
    { name: 'Total Over Due', value: indianCurrencyFormat(due, 0) },
    { name: 'Total Outstanding', value: indianCurrencyFormat(outstanding, 0) },
    { name: 'Total Payments', value: indianCurrencyFormat(paid, 0) },
    { name: 'Total Purchases', value: indianCurrencyFormat(sales, 0) },
    { name: 'Liquidation ( Aquaconnect )', value: indianCurrencyFormat(liquidation, 0) }, // show only above 20% of sales
  ]

  return (
    <div className="container px-4 pb-4">
      <div className="grid sm:mt-4 sm:grid-cols-3 sm:gap-4">
        {/* Old Account Statement not used now */}
        {stats.map((stat, statIdx) => (
          <Stat key={statIdx} title={stat.name} value={stat.value} index={statIdx} change="+4.5%" />
        ))}
        <div className="mt-5 flex items-center justify-center sm:mt-0">
          {accountStatementButton && (
            <Link className="w-full" href="/accountStatement">
              <Button text="View Account Statement" />
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}

export default AccountStatementStats
