//import connectedDB from '@/config/database';
import connectedDB from '@/app/config/database'
import Invoices from '@/app/models/Invoices'
// GET /api/invoices/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const invoiceWithItems = await Invoices.aggregate([
      { $match: { customerId: params.customerId } },
      {
        $lookup: {
          from: 'InvoiceItems',
          localField: 'invoiceId',
          foreignField: 'invoiceId',
          as: 'items',
        },
      },
      {
        $project: {
          _id: 0,
          invoiceId: 1,
          invoiceDate: 1,
          invoiceNumber: 1,
          invoiceStatus: 1,
          addressId: 1,
          customerId: 1,
          ageInDays: 1,
          ageTier: 1,
          subTotal: 1,
          total: 1,
          balance: 1,
          deliveryMode: 1,
          deliveryStatus: 1,
          dueDate: 1,
          items: 1,
        },
      },
      { $sort: { invoiceDate: -1 } },
    ])

    if (!invoiceWithItems) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify({ results: invoiceWithItems }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
