'use client'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import AddFarmerPopup from '@/customComponents/AddFarmerPopup'

import { retailerAtom } from '@/customComponents/providers'
import { useFetchFarmersVisitsByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useState } from 'react'
import { FarmerDetailView } from './components/farmerDetailsView'

export default function MyFarmers() {
  const customer = useAtomValue(retailerAtom)
  const farmersVisits = useFetchFarmersVisitsByCustomerId(customer.customerId)

  const [selectedFarmer, setSelectedFarmer] = useState(null)
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [showFarmerDetails, setShowFarmerDetails] = useState(false)

  const farmerClicked = (farmerName) => {
    let farmer = farmersVisits.results.find((farmer) => farmer.name === farmerName)
    if (farmer) {
      setSelectedFarmer(farmer)
      setShowFarmerDetails(true)
    }
  }

  const handleOpenPopup = () => setIsPopupOpen(true)
  const handleClosePopup = () => setIsPopupOpen(false)
  const handleEmailSubmit = (email) => {
    console.log('User email:', email)
    setIsPopupOpen(false)
  }

  return (
    <div className="flex min-h-screen flex-row overflow-y-auto scroll-smooth">
      <div className="flex w-screen flex-col">
        <div className="flex flex-wrap items-end justify-between gap-4">
          <div className="pb-6 mt-2 align-baseline sm:flex sm:justify-between">
            <div className="flex flex-col sm:flex-row">
              <p className="text-2xl font-bold">My Farmers</p>
            </div>
          </div>
          {/* <button
            type="button"
            onClick={handleOpenPopup}
            className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Add Farmer
          </button> */}
        </div>

        {/* <ul name="farmersList" className="mt-0 flex-1">
          {farmersVisits.results &&
            farmersVisits.results.map((farmer, index) => {
              // Get the correct farmer image
              const farmerImage =
                farmer.visits[0].image ||
                farmer.visits[farmer.visits.length - 1]?.image ||
                `https://placehold.co/400x600/png?font=roboto&text=${farmer.name[0]}`

              return (
                <li key={farmer.name}>
                  {index > 0 && <Divider soft={index > 0} />}
                  <div className="flex items-center justify-between">
                    <div className="flex gap-6 py-6">
                      <div className="w-32 shrink-0">
                        <img
                          className="rotate-270 scale-59 h-32 w-32 rounded-full shadow"
                          src={farmerImage}
                          alt={`${farmer.name}'s farm visit image`}
                        />
                     
                      </div>
                      <div className="space-y-1.5">
                        <div className="text-base/6 font-semibold">
                          <button className="text-start uppercase" name={farmer.name} onClick={farmerClicked}>
                            {farmer.name}
                          </button>
                        </div>
                        <div className="text-xs/6 text-zinc-500">
                          {farmer.visits[farmer.visits.length - 1]?.shortAddress.trim()}
                        </div>
                        <div className="text-xs/6 text-zinc-600">
                          {'Last visited on '}
                          {moment(farmer.visits[0]?.visitedDate).format('DD-MM-YYYY')}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge className="max-sm:hidden" color={farmer.farmType === 'Own' ? 'lime' : 'zinc'}>
                        {farmer.farmType}
                      </Badge>
                    </div>
                  </div>
                </li>
              )
            })}
        </ul> */}

        {/* Popup for adding a farmer */}
        <div className="flex justify-center">
          {isPopupOpen && (
            <AddFarmerPopup
              open={isPopupOpen}
              setOpen={setIsPopupOpen}
              onClose={handleClosePopup}
              onSubmit={handleEmailSubmit}
            />
          )}

          {/* Farmer detail view */}
          {showFarmerDetails && (
            <FarmerDetailView open={showFarmerDetails} setOpen={setShowFarmerDetails} farmer={selectedFarmer} />
          )}
        </div>
        <div className="overflow-x-auto">
          <Table className="w-full [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
            <TableHead>
              <TableRow>
                <TableHeader>Farmer</TableHeader>
                <TableHeader>Address</TableHeader>
                <TableHeader>Last Visit Date</TableHeader>
              </TableRow>
            </TableHead>
            <TableBody>
              {farmersVisits.results &&
                farmersVisits.results.map((farmer, index) => {
                  const farmerImage =
                    farmer.visits[0].image ||
                    farmer.visits[farmer.visits.length - 1]?.image ||
                    `https://placehold.co/400x600/png?font=roboto&text=${farmer.name[0]}`
                  return (
                    <TableRow
                      key={index}
                      href={'#'}
                      title={`farmer #${farmer.name}`}
                      name={farmer.name}
                      onClick={() => farmerClicked(farmer.name)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <img src={farmerImage} alt="farmers Image" className="h-6 w-6 rounded-full object-cover" />
                          <span>{farmer.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{farmer.visits[farmer.visits.length - 1]?.shortAddress.trim()}</TableCell>
                      <TableCell className="text-zinc-500">
                        {moment(farmer.visits[0]?.visitedDate).format('DD-MM-YYYY')}
                      </TableCell>
                    </TableRow>
                  )
                })}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
