import { indianCurrencyFormat } from '@/utils/formats'
import { getLastMonthLiquidationData } from '@/utils/myFarmerDetails'
import { useAtomValue } from 'jotai'
import { useEffect, useState } from 'react'
import { DesktopStat } from './common/DesktopStat'
import { dashboardAtom } from './providers'

const dashBoardGroup = (totalFarmers, potentialFarmers, lastMonthLiquidation) => [
  { name: 'Attached Farmers', value: totalFarmers },
  { name: 'Potential Farmers', value: potentialFarmers },
  { name: 'Last Month Liquidation', value: indianCurrencyFormat(lastMonthLiquidation, 0) },
]

export const MyFarmerDetails = () => {
  const { myFarmers, liquidation } = useAtomValue(dashboardAtom)

  const [myFarmerData, setMyFarmerData] = useState(() => dashBoardGroup(0, 0, 0))

  useEffect(() => {
    if (myFarmers) {
      setMyFarmerData(() =>
        dashBoardGroup(
          myFarmers.totalFarmers.length,
          myFarmers.potentialFarmers,
          getLastMonthLiquidationData(liquidation?.liqudationByYear)
        )
      )
    }
  }, [myFarmers, liquidation])

  return (
    <>
      <section className="mb-6">
        <div className="container mx-auto">
          <div className="mb-2">
            <h2 className="font-inter text-lg font-semibold capitalize text-[#111827]">My Farmers</h2>
          </div>
          <div className="grid rounded-lg border border-[#E5E7EB] bg-[#F9FAFB] p-6 sm:grid-cols-4 sm:gap-4">
            {myFarmerData.map((stat, statIdx) => (
              <DesktopStat key={statIdx} title={stat.name} value={stat.value} index={statIdx} change="+4.5%" />
            ))}
          </div>
        </div>
      </section>
    </>
  )
}
