# Zoho Payment Webhook Integration Guide

## Overview

This guide provides step-by-step instructions for implementing and configuring Zoho Payment webhooks in the AquaPartner application. Webhooks enable real-time payment status updates without manual polling.

## 🎯 **Benefits of Webhook Integration**

1. **Real-time Updates**: Instant notification of payment status changes
2. **Reduced API Calls**: No need to poll payment status repeatedly
3. **Better User Experience**: Immediate feedback on payment completion
4. **Automated Processing**: Trigger business logic automatically
5. **Reliability**: Zoho retries failed webhook deliveries

## 📋 **Prerequisites**

- AquaPartner application with Zoho Payment API integration
- MongoDB database for webhook event storage
- HTTPS endpoint (required for production)
- Zoho Payments account with webhook configuration access

## 🔧 **Step 1: Environment Configuration**

Add webhook configuration to your `.env.local` file:

```env
# Webhook Configuration
ZOHO_WEBHOOK_SECRET=your_webhook_secret_here
NEXT_PUBLIC_DOMAIN=https://your-domain.com

# Existing Zoho Configuration
ZOHO_PAY_ACCOUNT_ID=your_account_id
MONGODB_URI=your_mongodb_connection_string
```

## 📊 **Step 2: Database Models**

The webhook integration uses the following models:

### WebhookEvent Model
- Stores all incoming webhook events
- Tracks processing status and attempts
- Prevents duplicate processing
- Maintains audit trail

### PaymentTransaction Model
- Links webhook events to payment sessions
- Tracks status updates from webhooks
- Stores payment metadata

## 🌐 **Step 3: Webhook Endpoints**

The integration provides several webhook endpoints:

### 1. Main Webhook Receiver
```
POST /api/zoho/webhooks/payment
```
- Receives webhooks from Zoho
- Verifies signatures (if configured)
- Processes events asynchronously
- Prevents duplicate processing

### 2. Webhook Management
```
GET /api/zoho/webhooks/manage
POST /api/zoho/webhooks/manage
```
- View webhook statistics
- Reprocess failed webhooks
- Cleanup old events
- Monitor webhook health

### 3. Webhook Testing
```
GET /api/zoho/webhooks/test
POST /api/zoho/webhooks/test
```
- Test webhook processing
- Generate sample webhook events
- Validate webhook handling logic

## ⚙️ **Step 4: Zoho Dashboard Configuration**

### 4.1 Access Zoho Payments Dashboard
1. Log in to [Zoho Payments](https://payments.zoho.com)
2. Navigate to **Settings** → **Webhooks**
3. Click **Add Webhook**

### 4.2 Configure Webhook Settings
```
Webhook URL: https://your-domain.com/api/zoho/webhooks/payment
Content Type: application/json
Secret: your_webhook_secret_here (optional but recommended)
```

### 4.3 Select Events
Enable the following events:
- ✅ `payment.succeeded` - Payment completed successfully
- ✅ `payment.failed` - Payment failed
- ✅ `payment.pending` - Payment is pending
- ✅ `payment.cancelled` - Payment was cancelled
- ✅ `payment_session.expired` - Payment session expired
- ✅ `refund.succeeded` - Refund completed (optional)
- ✅ `refund.failed` - Refund failed (optional)

## 🧪 **Step 5: Testing Webhook Integration**

### 5.1 Test Webhook Endpoint
```bash
curl -X GET https://your-domain.com/api/zoho/webhooks/payment
```

Expected response:
```json
{
  "message": "Zoho Payment Webhook Endpoint",
  "endpoint": "/api/zoho/webhooks/payment",
  "supported_events": [...],
  "configuration": {
    "webhook_url": "https://your-domain.com/api/zoho/webhooks/payment",
    "signature_verification": true
  }
}
```

### 5.2 Test with Sample Data
```bash
curl -X POST https://your-domain.com/api/zoho/webhooks/test \
  -H "Content-Type: application/json" \
  -d '{
    "event_type": "payment_success",
    "create_transaction": true
  }'
```

### 5.3 Verify Processing
```bash
curl -X GET https://your-domain.com/api/zoho/webhooks/manage
```

## 📈 **Step 6: Monitoring and Management**

### 6.1 Webhook Statistics
Monitor webhook performance:
- Total events received
- Processing success rate
- Failed events count
- Average processing time

### 6.2 Failed Event Handling
Automatically retry failed webhooks:
```bash
curl -X POST https://your-domain.com/api/zoho/webhooks/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "reprocess_failed"}'
```

### 6.3 Cleanup Old Events
Remove processed events older than 30 days:
```bash
curl -X POST https://your-domain.com/api/zoho/webhooks/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "cleanup_old"}'
```

## 🔒 **Step 7: Security Best Practices**

### 7.1 Signature Verification
Always verify webhook signatures in production:
```javascript
const expectedSignature = crypto
  .createHmac('sha256', process.env.ZOHO_WEBHOOK_SECRET)
  .update(payload)
  .digest('hex')
```

### 7.2 HTTPS Requirement
- Use HTTPS for all webhook endpoints
- Zoho requires SSL certificates for production

### 7.3 Rate Limiting
Implement rate limiting to prevent abuse:
```javascript
// Example: Max 100 webhooks per minute per IP
const rateLimit = 100
const timeWindow = 60000 // 1 minute
```

## 🚀 **Step 8: Production Deployment**

### 8.1 Pre-deployment Checklist
- [ ] HTTPS certificate configured
- [ ] Webhook secret configured
- [ ] Database models deployed
- [ ] Monitoring setup
- [ ] Error alerting configured

### 8.2 Deployment Steps
1. Deploy application with webhook endpoints
2. Configure webhook URL in Zoho dashboard
3. Test with small payment amounts
4. Monitor webhook processing
5. Enable for production traffic

### 8.3 Post-deployment Monitoring
- Monitor webhook delivery success rate
- Track payment processing times
- Set up alerts for failed webhooks
- Regular cleanup of old webhook events

## 🔧 **Troubleshooting**

### Common Issues

#### 1. Webhook Not Received
**Symptoms**: No webhook events in database
**Solutions**:
- Verify webhook URL is accessible
- Check HTTPS certificate
- Confirm webhook is enabled in Zoho dashboard

#### 2. Signature Verification Failed
**Symptoms**: 401 errors in webhook logs
**Solutions**:
- Verify webhook secret matches Zoho configuration
- Check signature calculation logic
- Ensure raw body is used for signature verification

#### 3. Duplicate Processing
**Symptoms**: Same event processed multiple times
**Solutions**:
- Check event_id uniqueness constraint
- Verify duplicate detection logic
- Review webhook retry configuration

#### 4. Processing Failures
**Symptoms**: Webhooks received but not processed
**Solutions**:
- Check transaction existence
- Verify status mapping logic
- Review error logs for specific issues

## 📊 **Webhook Event Flow**

```mermaid
graph TD
    A[Payment Completed] --> B[Zoho Sends Webhook]
    B --> C[Webhook Received]
    C --> D[Verify Signature]
    D --> E[Check Duplicates]
    E --> F[Store Webhook Event]
    F --> G[Process Asynchronously]
    G --> H[Update Transaction Status]
    H --> I[Send Notifications]
    I --> J[Mark as Processed]
```

## 📋 **API Reference**

### Webhook Event Object
```json
{
  "event_id": "evt_123456789",
  "event_type": "payment_success",
  "payment_session_id": "ps_123456789",
  "payment_id": "pay_123456789",
  "amount": 1000.50,
  "currency": "INR",
  "customer_id": "CUST-001",
  "processed": true,
  "webhook_received_at": "2025-06-12T14:30:00Z"
}
```

### Supported Event Types
| Event Type | Description | Transaction Status |
|------------|-------------|-------------------|
| `payment_success` | Payment completed | `completed` |
| `payment_failed` | Payment failed | `failed` |
| `payment_pending` | Payment pending | `pending` |
| `payment_cancelled` | Payment cancelled | `cancelled` |
| `session_expired` | Session expired | `expired` |
| `refund_success` | Refund completed | `refunded` |

## 🎯 **Next Steps**

1. **Implement Business Logic**: Add custom processing for different event types
2. **Add Notifications**: Send emails/SMS for payment events
3. **Analytics**: Track payment success rates and patterns
4. **Advanced Features**: Implement partial refunds, recurring payments

## 📞 **Support**

For webhook integration support:
1. Check webhook logs in `/api/zoho/webhooks/manage`
2. Test with `/api/zoho/webhooks/test`
3. Review Zoho Payments documentation
4. Contact development team for assistance

The webhook integration provides a robust foundation for real-time payment processing and can be extended based on your specific business requirements.
