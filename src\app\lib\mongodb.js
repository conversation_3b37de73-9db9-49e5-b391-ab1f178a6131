import { MongoClient } from 'mongodb';

const uri = 'mongodb+srv://admin:<EMAIL>/aquapartner?retryWrites=true&w=majority&appName=aquapartner-dev';
const options = { useUnifiedTopology: true, useNewUrlParser: true };

let client;
let clientPromise;

/*if (!process.env.MONGODB_URI) {
  throw new Error('Please add your MongoDB URI to .env.local');
}

if (process.env.NODE_ENV === 'development') {
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
} */

client = new MongoClient(uri, options);
clientPromise = client.connect();

export default clientPromise;