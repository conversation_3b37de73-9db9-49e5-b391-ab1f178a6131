import { Schema, model, models } from 'mongoose'

const LiquidationSchema = new Schema(
  {
    liquidateId: String,
    aquapartner: String,
    customerId: String,
    date: Date,
    farmerName: String,
    image: String,
    imageUrl: String,
    mobileNumber: String,
    noOfProducts: Number,
    remarks: String,
    waResponse: String,
    total:Number
  },
  { collection: 'Liquidation' }
)

const Liquidation = models.Liquidation || model('Liquidation', LiquidationSchema)

export default Liquidation
