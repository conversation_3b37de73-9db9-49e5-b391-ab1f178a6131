'use client';
import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const barChartData = ({ labels, values, label, type } = {}) => {
  const data = {
    type: type || 'line',
    labels: labels || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: [
      {
        label: label || 'Purchase',
        data: values || [102189, 47748, 0, 104496, 163864, 155013, 147225],

        backgroundColor: (context) => {
          const ctx = context.chart.ctx;
          const gradient = ctx.createLinearGradient(0, 0, 0, 190);
          gradient.addColorStop(0, 'rgba(127, 86, 217, 1)');
          gradient.addColorStop(1, 'rgba(127, 86, 217, 0)');

          return gradient;
        },
        borderColor: ['rgb(54, 162, 235, 1)'],
        borderWidth: 0,
      },
    ],
  };

  return data;
};

const BarChart = ({ data, option }) => {
  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: '  Purchases',
        align: 'start',
        padding: {
          top: 10,
          left: 30,
          bottom: 10,
          right: 10,
        },
        font: {
          size: 18,
        },
      },
      tooltip: {
        titleFont: {},
        bodyFont: {},
        displayColors: false,
        backgroundColor: 'grey',
        textColor: 'white',
        yAlign: 'bottom',
        callbacks: {
          labelTextColor: (ctx) => {
            return 'white';
          },

          // beforeTitle: function (context) {
          //   return 'before';
          // },
          title: (ctx) => {
            return `${ctx[0].dataIndex} - ${ctx[0].label}`;
          },
          // afterTitle: function (context) {
          //   return 'after';
          // },
          // beforeBody: function (context) {
          //   return 'before Body';
          // },
          // beforeLabel: function (context) {
          //   return 'before Label';
          // },
          // afterLabel: function (context) {
          //   return 'after Label';
          // },
          // afterBody: function (context) {
          //   return 'after Body';
          // },
          // beforeFooter: function (context) {
          //   return 'before Footer';
          // },
          // footer: function (context) {
          //   return ' Footer \n Item';
          // },
          // afterFooter: function (context) {
          //   return 'after Footer';
          // },
        },
      },
    },

    hover: {
      mode: 'label',
    },
    gridLines: {
      display: false,
    },
    scales: {
      x: {
        display: true,
        scaleLabel: {
          display: true,
        },
        grid: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        border: {
          dash: [3, 5],
        },
        grid: {
          display: true,
          drawOnChartArea: true,
          drawTicks: true,
          lineWidth: 2,
        },
        ticks: {
          stepSize: 35000,
          callback: function (value, index, values) {
            if (value >= 1000) {
              return value / 1000 + 'K';
            }
            return value;
          },
        },
      },
    },
  };
  return <Bar options={options} data={barChartData()} />;
};

export default BarChart;
