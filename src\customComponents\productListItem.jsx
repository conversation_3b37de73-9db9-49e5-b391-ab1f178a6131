import React from 'react';
import { getBrands } from '@/data/ProductCatalogue';
import Image from 'next/image';

const productListItem = ({ children }) => {
  return (
    <div className="overflow-hidden rounded-md bg-white shadow">
      <ul role="list" className="divide-y divide-gray-200">
        (
        <li key={item.name} className="px-6 py-4">
          <Image
            src={item.image}
            height={30}
            width={30}
            alt={item.name}></Image>
        </li>
        )
      </ul>
    </div>
  );
};

export { productListItem };
