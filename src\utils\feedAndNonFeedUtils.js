import moment from 'moment';
import _ from 'lodash';
import { convertToNumber } from './formats';
import { salesMasterData } from '@/data/salesMasterData';

const fetchPurchaseOrders = async (customerId) => {
  // const masterData = await fetchMasterDataByCustomerId(customerId);
  const totalSalesMasterData = salesMasterData;
  return totalSalesMasterData;
};

const filterPurchaseOrdersByYear = (orders, year) => {
  return orders
    .filter(
      (order) =>
        moment(order.invoiceDate, 'DD MMM, YYYY 00:00:00').year() === year
    )
    .map((order) => {
      return {
        total: convertToNumber(order.totalSalesInclTax),
        invoiceDate: order.invoiceDate,
        type: order.categoryType,
      };
    });
};

const groupByMonthAndType = (data) => {
  const feedData = {};
  const nonFeedData = {};

  data.forEach((item) => {
    const month = moment(item.invoiceDate, 'DD MMM YYYY').format('MMM');
    const type = item.type;

    if (type === 'Feed') {
      if (!feedData[month]) {
        feedData[month] = 0;
      }
      feedData[month] += item.total;
    } else if (type === 'Non Feed') {
      if (!nonFeedData[month]) {
        nonFeedData[month] = 0;
      }
      nonFeedData[month] += item.total;
    }
  });

  return { feedData, nonFeedData };
};

export const getFeedAndNonFeedAmountByMonthForYear = async (
  customerId,
  year
) => {
  const purchaseOrders = await fetchPurchaseOrders(customerId);
  const purchaseOrdersForYear = filterPurchaseOrdersByYear(
    purchaseOrders,
    year
  );

  const purchaseOrdersGroupedByMonthAndType = groupByMonthAndType(
    purchaseOrdersForYear
  );

  return purchaseOrdersGroupedByMonthAndType;
};
