import connectedDB from '@/app/config/database'
import CreditNotes from '@/app/models/CreditNotes'

// GET /api/creditNotes/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const creditNotes = await CreditNotes.find({
      customerId: params.id,
    }).sort({ date1: -1 })

    if (!creditNotes) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: creditNotes }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
