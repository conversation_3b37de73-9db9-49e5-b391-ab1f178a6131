import _ from 'lodash'
import moment from 'moment'
import { salesOrdersList } from '../data/salesOrderData'
import { convertToNumber } from './formats'

const fetchSalesOrders = async (customerId) => {
  const salesOrders = salesOrdersList
  return salesOrders
}

const filterSalesOrdersByYear = (orders, year) => {
  return orders
    .filter((order) => moment(order.saleOrderDate, 'DD MMM YYYY').year() === year)
    .map((order) => {
      return {
        total: convertToNumber(order.total),
        orderDate: order.saleOrderDate,
      }
    })
}

const groupOrdersByMonth = (orders) => {
  return _.groupBy(orders, (order) => moment(order.orderDate, 'DD MMM YYYY').format('MMM'))
}

const calculateTotalAmountByMonth = (groupedOrders) => {
  return _.mapValues(groupedOrders, (orders) => {
    return _.sumBy(orders, (value) => {
      return Number(value.total)
    })
  })
}

export const getSalesAmountByMonthForYear = async (customerId, year) => {
  const salesOrders = await fetchSalesOrders(customerId)
  const salesOrdersForYear = filterSalesOrdersByYear(salesOrders, year)
  const salesOrdersGroupedByMonth = groupOrdersByMonth(salesOrdersForYear)
  const salesAmountByMonth = calculateTotalAmountByMonth(salesOrdersGroupedByMonth)
  return salesAmountByMonth
}
