import connectedDB from '@/app/config/database'
import SalesOrderItems from '@/app/models/SaleOrderItems'

// GET /api/saleOrderItems/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const saleOrderItems = await SalesOrderItems.find({
      salesOrderId: params.id,
    })

    // console.log(saleOrderItems);
    if (!saleOrderItems) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(saleOrderItems), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
