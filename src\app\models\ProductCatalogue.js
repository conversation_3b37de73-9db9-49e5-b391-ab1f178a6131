import { Schema, model, models } from 'mongoose'

const ProductSchema = new Schema({
  productName: String,
  category: String,
  categoryType: String,
  subCategory: String,
  tagLine: String,
  productImage: String,
  content: String,
  sortOrder: Number,
  status: String,
  productTag: String
})

// Define the Main Schema
const CatalogSchema = new Schema(
  {
    name: String,
    image: String,
    sortOrder: Number,
    status: String,
    products: [ProductSchema], // Embed the Product Schema
  },
  { collection: 'ProductCatalogue' }
)

const ProductCatalogue = models.ProductCatalogue || model('ProductCatalogue', CatalogSchema)

export default ProductCatalogue
