'use client'

import { Badge } from '@/components/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { convertToNumber, indianCurrencyFormat } from '@/utils/formats'
import { useFetchSalesOrdersByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { SalesOrderDetailsNew } from './saleOrderDetailsNew'

export const SalesOrderPage = () => {
  const customer = useAtomValue(retailerAtom)
  const [selectedOrder, setSelectedOrder] = useState({})
  const [loading, setLoading] = useState(true)

  var data = useFetchSalesOrdersByCustomerId(customer.customerId)

  const [isPopupOpen, setIsPopupOpen] = useState(false)

  const handleOpenPopup = ({ selectedSalesOrder }) => {
    setSelectedOrder(selectedSalesOrder)
    setIsPopupOpen(true)
  }

  // Use useEffect to prevent direct setError during render
  useEffect(() => {
    if (data?.results) {
      setLoading((prev) => false)
    }
  }, [data])

  return (
    <>
      {loading && <LoadingScreen />}
      <Table className="mt-2 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
        <TableHead>
          <TableRow>
            <TableHeader>Order date</TableHeader>
            <TableHeader className="text-right">Total</TableHeader>
            <TableHeader>Order Number</TableHeader>
            <TableHeader>Status</TableHeader>
          </TableRow>
        </TableHead>
        <TableBody className="pl-4">
          {data.results &&
            data.results.map(
              (order, index) =>
                order.items && (
                  <TableRow
                    className="hover:bg-blue-50"
                    key={index}
                    title={`Order # No`}
                    onClick={() => handleOpenPopup({ selectedSalesOrder: order })}
                  >
                    <TableCell>{moment(order.saleOrderDate, 'DD MMM YYYY').format('DD-MM-YYYY')}</TableCell>
                    <TableCell className="text-right">{indianCurrencyFormat(convertToNumber(order.total))}</TableCell>

                    <TableCell className="text-zinc-500">{order.salesOrderNumber}</TableCell>
                    <TableCell>
                      <Badge
                        color={
                          order.invoicedStatus === 'invoiced'
                            ? 'blue'
                            : order.invoicedStatus === 'Partially Delivered'
                              ? 'yellow'
                              : order.invoicedStatus === 'Rejected'
                                ? 'red'
                                : 'yellow'
                        }
                      >
                        {order.invoicedStatus}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Link
                        href="#"
                        onClick={() => handleOpenPopup({ selectedSalesOrder: order })}
                        className="text-blue-700"
                      >
                        View
                      </Link>
                    </TableCell>
                  </TableRow>
                )
            )}
        </TableBody>
      </Table>

      {isPopupOpen && (
        <SalesOrderDetailsNew
          orderDetails={selectedOrder}
          open={isPopupOpen}
          setOpen={setIsPopupOpen}
          customer={customer}
        />
      )}
    </>
  )
}
