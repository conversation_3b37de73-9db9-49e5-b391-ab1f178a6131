import connectedDB from '@/app/config/database'
import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * GET /api/zoho/health
 * Health check and system status for Zoho Payment integration
 */
export async function GET() {
  const healthCheck = {
    timestamp: new Date().toISOString(),
    service: 'Zoho Payment Integration',
    version: '1.0.0',
    status: 'checking...',
    checks: {},
  }

  try {
    // Check database connection
    try {
      await connectedDB()
      healthCheck.checks.database = {
        status: 'healthy',
        message: 'Database connection successful',
      }
    } catch (error) {
      healthCheck.checks.database = {
        status: 'unhealthy',
        message: 'Database connection failed',
        error: error.message,
      }
    }

    // Check environment variables
    const requiredEnvVars = ['ZOHO_PAY_ACCOUNT_ID', 'MONGODB_URI']

    const missingEnvVars = requiredEnvVars.filter((varName) => !process.env[varName])

    healthCheck.checks.environment = {
      status: missingEnvVars.length === 0 ? 'healthy' : 'unhealthy',
      message:
        missingEnvVars.length === 0
          ? 'All required environment variables are set'
          : `Missing environment variables: ${missingEnvVars.join(', ')}`,
      required_variables: requiredEnvVars,
      missing_variables: missingEnvVars,
    }

    // Check Zoho token status
    try {
      const token = await zohoPaymentService.getValidAccessToken()
      healthCheck.checks.zoho_auth = {
        status: 'healthy',
        message: 'Zoho authentication token is available',
        has_token: !!token,
        token_source: 'external_service',
      }
    } catch (error) {
      healthCheck.checks.zoho_auth = {
        status: 'unhealthy',
        message: 'Zoho authentication token not available',
        error: error.message,
        token_source: 'external_service',
        note: 'Tokens are managed by external backend service',
      }
    }

    // Check Zoho API connectivity
    try {
      // Try to make a simple API call to test connectivity
      await zohoPaymentService.getValidAccessToken()
      healthCheck.checks.zoho_api = {
        status: 'healthy',
        message: 'Zoho API is accessible',
      }
    } catch (error) {
      healthCheck.checks.zoho_api = {
        status: 'unhealthy',
        message: 'Zoho API connectivity failed',
        error: error.message,
      }
    }

    // Overall health status
    const allChecks = Object.values(healthCheck.checks)
    const unhealthyChecks = allChecks.filter((check) => check.status === 'unhealthy')

    healthCheck.status = unhealthyChecks.length === 0 ? 'healthy' : 'unhealthy'
    healthCheck.summary = {
      total_checks: allChecks.length,
      healthy_checks: allChecks.length - unhealthyChecks.length,
      unhealthy_checks: unhealthyChecks.length,
    }

    // Add configuration info
    healthCheck.configuration = {
      account_id: process.env.ZOHO_PAY_ACCOUNT_ID ? 'configured' : 'missing',
      webhook_secret: process.env.ZOHO_WEBHOOK_SECRET ? 'configured' : 'not configured',
      domain: process.env.NEXT_PUBLIC_DOMAIN || 'not configured',
    }

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503

    return new Response(JSON.stringify(healthCheck), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
    })
  } catch (error) {
    return new Response(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        service: 'Zoho Payment Integration',
        status: 'error',
        message: 'Health check failed',
        error: error.message,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }
}

/**
 * POST /api/zoho/health
 * Run detailed diagnostics
 */
export async function POST(request) {
  try {
    const body = await request.json()
    const { include_sensitive = false } = body

    const diagnostics = {
      timestamp: new Date().toISOString(),
      service: 'Zoho Payment Integration',
      diagnostics: {},
    }

    // Database diagnostics
    try {
      await connectedDB()

      // Check collections
      const mongoose = require('mongoose')
      const collections = await mongoose.connection.db.listCollections().toArray()

      diagnostics.diagnostics.database = {
        status: 'healthy',
        connection_state: mongoose.connection.readyState,
        collections: collections.map((col) => col.name),
      }
    } catch (error) {
      diagnostics.diagnostics.database = {
        status: 'unhealthy',
        error: error.message,
      }
    }

    // Token diagnostics
    try {
      const PaymentAccessToken = require('@/app/models/PaymentAccessToken').default
      const tokenRecord = await PaymentAccessToken.findOne({})

      if (tokenRecord && tokenRecord.access_token) {
        diagnostics.diagnostics.tokens = {
          status: 'healthy',
          message: 'Token available from external service',
          has_token: true,
          token_source: 'external_service',
        }

        if (include_sensitive) {
          diagnostics.diagnostics.tokens.access_token_preview = tokenRecord.access_token
            ? `${tokenRecord.access_token.substring(0, 10)}...`
            : 'none'
        }
      } else {
        diagnostics.diagnostics.tokens = {
          status: 'unhealthy',
          message: 'No token found in database',
          has_token: false,
          token_source: 'external_service',
          note: 'Check external token management service',
        }
      }
    } catch (error) {
      diagnostics.diagnostics.tokens = {
        status: 'error',
        error: error.message,
        token_source: 'external_service',
      }
    }

    // Transaction diagnostics
    try {
      const PaymentTransaction = require('@/app/models/PaymentTransaction').default
      const transactionStats = await PaymentTransaction.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            total_amount: { $sum: '$amount' },
          },
        },
      ])

      const totalTransactions = await PaymentTransaction.countDocuments()
      const recentTransactions = await PaymentTransaction.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
      })

      diagnostics.diagnostics.transactions = {
        status: 'healthy',
        total_transactions: totalTransactions,
        recent_transactions_24h: recentTransactions,
        status_breakdown: transactionStats,
      }
    } catch (error) {
      diagnostics.diagnostics.transactions = {
        status: 'error',
        error: error.message,
      }
    }

    // API endpoints status
    diagnostics.diagnostics.endpoints = {
      authentication: '/api/zoho/auth/*',
      payments: '/api/zoho/payments/*',
      refunds: '/api/zoho/refunds/*',
      webhooks: '/api/zoho/webhooks/*',
      health: '/api/zoho/health',
    }

    return new Response(JSON.stringify(diagnostics), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    return new Response(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        service: 'Zoho Payment Integration',
        status: 'error',
        message: 'Diagnostics failed',
        error: error.message,
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }
}
