'use client';
import React from 'react';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJ<PERSON>,
  Tooltip,
  Legend,
  ArcElement,
  Title,
  SubTitle,
} from 'chart.js';
import { pieChartData } from '@/chartData';

ChartJS.register(Tooltip, Legend, ArcElement, Title, SubTitle);

const PieChart = () => {
  const options = {};
  return <Pie options={options} data={pieChartData} />;
};
export default PieChart;
