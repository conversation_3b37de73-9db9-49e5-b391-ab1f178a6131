import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * POST /api/zoho/auth/refresh
 * Manually refresh access token
 */
export async function POST(request) {
  try {
    const result = await zohoPaymentService.getValidAccessToken()

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Access token refreshed successfully',
        data: {
          token_refreshed: true,
          expires_at: new Date(Date.now() + 3600 * 1000), // Approximate expiry
        },
      }),
      { status: 200 }
    )
  } catch (error) {
    console.error('Error refreshing token:', error.message)

    return new Response(
      JSON.stringify({
        error: 'Token refresh failed',
        message: error.message,
        details: 'Please check your OAuth configuration or run initial setup',
      }),
      { status: 500 }
    )
  }
}

/**
 * GET /api/zoho/auth/refresh
 * Check token status
 */
export async function GET() {
  try {
    const token = await zohoPaymentService.getValidAccessToken()

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Token is valid',
        data: {
          token_valid: true,
          has_access_token: !!token,
        },
      }),
      { status: 200 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Token validation failed',
        error: error.message,
        data: {
          token_valid: false,
          needs_setup: error.message.includes('No Zoho Payment token found'),
        },
      }),
      { status: 200 }
    )
  }
}
