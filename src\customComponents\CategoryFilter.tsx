import React, { useRef } from 'react'

interface CategoryFilterProps {
  // Callback function to handle category selection changes
  onFilterChange: (category: string) => void

  // Array of subcategory names
  subCategories: string[]

  // Currently selected category
  selectedCategory: string

  containerRef?: HTMLDivElement
}

export interface ChildRef {
  scrollToTop: () => void
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  subCategories,
  onFilterChange,
  selectedCategory,
  containerRef,
}) => {
  // Ref for the 'All' category button
  const allCategoryRef = useRef<HTMLButtonElement | null>(null)

  // Function to handle category button clicks
  const handleCategoryClick = (category: string) => {
    onFilterChange(category)
  }

  // useEffect to scroll the 'All' button into view when it's selected on component mount
  /*   useEffect(() => {
    if (selectedCategory === 'All' && allCategoryRef.current) {
      allCategoryRef.current.scrollIntoView({
        behavior: 'smooth',
        inline: 'center',
      })
    }
  }, [selectedCategory]) */

  return (
    <div className="relative w-full">
      {/* Left Gradient Overlay */}
      <div className="pointer-events-none absolute bottom-0 left-0 top-0 z-10 w-8 bg-gradient-to-r from-white to-transparent" />

      {/* Right Gradient Overlay */}
      <div className="pointer-events-none absolute bottom-0 right-0 top-0 z-10 w-8 bg-gradient-to-l from-white to-transparent" />

      {/* Scrollable Container */}
      <div className="no-scrollbar scroll-hidden overflow-x-auto" style={{ overscrollBehaviorX: 'contain' }}>
        <div className="mt-2 flex min-w-min gap-2 px-4">
          {subCategories.map((category) => (
            <button
              key={category}
              onClick={() => handleCategoryClick(category)}
              ref={category === 'All' ? allCategoryRef : null} // Assign ref to 'All' category
              className={`whitespace-nowrap rounded-full border border-gray-300 px-4 py-2 text-sm transition-all duration-200 ${
                selectedCategory === category ? 'bg-yellow-100 text-black' : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default CategoryFilter
