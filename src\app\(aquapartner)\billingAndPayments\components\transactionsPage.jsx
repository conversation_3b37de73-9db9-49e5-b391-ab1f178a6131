'use client'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { indianCurrencyFormat } from '@/utils/formats'
import { useFetchTransactionsByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useEffect, useState } from 'react'

export const TransactionsPage = () => {
  const [loading, setLoading] = useState(true)
  const customer = useAtomValue(retailerAtom)
  const transactions = useFetchTransactionsByCustomerId(customer.customerId)

  useEffect(() => {
    if (transactions?.results) {
      setLoading((prev) => false)
    }
  }, [transactions])

  return (
    <>
      {loading && <LoadingScreen />}
      <Table className="mt-2 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
        <TableHead>
          <TableRow>
            <TableHeader>Date</TableHeader>
            <TableHeader>Ref No</TableHeader>
            <TableHeader className="text-right">Amount</TableHeader>
            <TableHeader>Mode</TableHeader>
          </TableRow>
        </TableHead>
        <TableBody>
          {transactions.results &&
            transactions.results.map((transaction, index) => (
              <TableRow title={`Order # No`} key={index}>
                <TableCell>{moment(transaction.paymentsDate).format('DD-MM-YYYY')}</TableCell>
                <TableCell>{transaction.paymentsNumber}</TableCell>
                <TableCell className="text-right">{indianCurrencyFormat(transaction.amount)}</TableCell>
                <TableCell>{transaction.paymentsMode}</TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </>
  )
}
