import { Schema, model, models } from 'mongoose'

const SalesOrderSchema = new Schema(
  {
    salesOrderId: String,
    salesOrderNumber: String,
    customerId: String,
    subTotal: Number,
    total: Number,
    createdTime: Date,
    lastModifiedTime: Date,
    invoicedStatus: String,
    paidStatus: String,
    salesChannel: String,
    paymentTermsLabel: String,
    orderSource: String,
    saleOrderDate: String,
    addressId: String,
  },
  { collection: 'SalesOrder' }
)

const SalesOrders = models.SalesOrder || model('SalesOrder', SalesOrderSchema)

export default SalesOrders
