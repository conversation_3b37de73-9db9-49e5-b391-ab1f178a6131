import { Schema, model, models } from 'mongoose';

const SalesOrderItemSchema = new Schema(
  {
    productId: String,
    itemName: String,
    total: String,
    salesOrderId: String,
    itemPrice: String,
    hsnSac: Number,
    placeOfSupply: String,
    entityDiscountPercent: String,
    createdTime: Date,
    lastModifiedTime: Date,
    quantityPacked: Number,
    nonPackageQuantity: Number,
    quantityDelivered: Number,
    invoicedQuantityCancelled: Number,
    quantityDropshipped: Number,
    manuallyFulfilledQuantity: Number,
    salesVertices: Number,
    sno: Number,
  },
  { collection: 'SaleOrderItems' }
);

const SalesOrderItems =
  models.SaleOrderItems || model('SaleOrderItems', SalesOrderItemSchema);

export default SalesOrderItems;
