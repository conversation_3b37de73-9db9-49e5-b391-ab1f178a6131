// import { customerPayments } from '@/data/paymentsByCustomer'
import _ from 'lodash'
import moment from 'moment'
import { convertToNumber } from './formats'

const filterPaymentsByYear = (payments, year) => {
  return payments
    .filter((payment) => moment(payment.paymentsDate, 'DD MMM YYYY').year() === year)
    .map((payment) => {
      return {
        amount: convertToNumber(payment.amount),
        paymentsDate: payment.paymentsDate,
      }
    })
}

const groupOrdersByMonth = (payments) => {
  return _.groupBy(payments, (payment) => moment(payment.paymentsDate, 'DD MMM YYYY').format('MMM'))
}

const calculateTotalAmountByMonth = (groupedPayments) => {
  return _.mapValues(groupedPayments, (payments) => {
    return _.sumBy(payments, (payment) => {
      return Number(payment.amount)
    })
  })
}

const getPaymentsByMonthsForYear = async (customerId, year) => {
  const payments = await fetchPaymentsList(customerId)
  const paymentsForYear = filterPaymentsByYear(payments, year)
  const paymentsGroupedByMonth = groupOrdersByMonth(paymentsForYear)
  const paymentsByMonth = calculateTotalAmountByMonth(paymentsGroupedByMonth)
  return paymentsByMonth
}

const calculateTicketsWon = (totalPurchase, payment) => {
  let purchaseTicketWon = 0

  if (totalPurchase >= 1800000) {
    purchaseTicketWon = 3
  } else if (totalPurchase >= 1200000) {
    purchaseTicketWon = 2
  } else if (totalPurchase >= 600000) {
    purchaseTicketWon = 1
  }

  let paymentTicketWon = 0

  if (payment >= 1800000) {
    paymentTicketWon = 3
  } else if (payment >= 1200000) {
    paymentTicketWon = 2
  } else if (payment >= 600000) {
    paymentTicketWon = 1
  }

  return Math.min(purchaseTicketWon, paymentTicketWon)
}

const calculatePercentageCompleted = (purchasePending, paymentPending) => {
  let purchasePercentage = ((600000 - purchasePending) / 600000) * 100
  let paymentPercentage = ((600000 - paymentPending) / 600000) * 100

  return ((purchasePercentage + paymentPercentage) / 2).toFixed(0)
}

export { calculatePercentageCompleted, calculateTicketsWon, getPaymentsByMonthsForYear }
