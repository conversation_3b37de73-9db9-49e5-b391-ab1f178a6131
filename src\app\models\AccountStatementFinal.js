import { Schema, model, models } from 'mongoose'

const AccountStatementFinalSchema = new Schema(
  {
    txnDate: Date,
    vchType: String,
    vchNumber: String,
    particulars: String,
    amount: Number,
    credit: Number,
    debit: Number,
    customerId: String,
    customerName: String,
    transType: String,
    tax: Number,
    tds: Number,
  },
  { collection: 'AccountStatementFinal' }
)

const AccountStatementFinal =
  models.AccountStatementFinal || model('AccountStatementFinal', AccountStatementFinalSchema)

export default AccountStatementFinal
