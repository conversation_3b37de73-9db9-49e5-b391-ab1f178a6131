import { Schema, model, models } from 'mongoose';

const InvoiceItemSchema = new Schema(
  {
    productId: String,
    itemName: String,
    invoiceId: String,
    quantity: Number,
    discountAmountBcy: String,
    subTotal: String,
    total: String,
    createdTime: Date,
    lastModifiedTime: Date,
    productCategory: String,
    hsnSac: Number,
    placeOfSupply: String,
    itemPrice: String,
    source: String,
  },
  { collection: 'InvoiceItems' }
);

const InvoiceItems =
  models.InvoiceItems || model('InvoiceItems', InvoiceItemSchema);

export default InvoiceItems;
