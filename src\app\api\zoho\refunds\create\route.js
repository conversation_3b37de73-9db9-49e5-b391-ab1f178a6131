import zohoPaymentService from '@/app/lib/zohoPaymentService'

/**
 * POST /api/zoho/refunds/create
 * Create a refund for a payment
 */
export async function POST(request) {
  try {
    const body = await request.json()

    const { payment_id, amount, reason = 'Customer request', reference_id } = body

    // Validate required fields
    if (!payment_id) {
      return new Response(
        JSON.stringify({
          error: 'Missing payment ID',
          message: 'payment_id is required to create a refund',
        }),
        { status: 400 }
      )
    }

    if (!amount) {
      return new Response(
        JSON.stringify({
          error: 'Missing amount',
          message: 'amount is required to create a refund',
        }),
        { status: 400 }
      )
    }

    // Validate amount
    const numericAmount = parseFloat(amount)
    if (isNaN(numericAmount) || numericAmount <= 0) {
      return new Response(
        JSON.stringify({
          error: 'Invalid amount',
          message: 'Amount must be a positive number',
        }),
        { status: 400 }
      )
    }

    // Validate reason length
    if (reason && reason.length > 500) {
      return new Response(
        JSON.stringify({
          error: 'Invalid reason',
          message: 'Reason must be 500 characters or less',
        }),
        { status: 400 }
      )
    }

    // First, get the payment details to validate
    const payment = await zohoPaymentService.getPayment(payment_id)

    if (!payment) {
      return new Response(
        JSON.stringify({
          error: 'Payment not found',
          message: 'No payment found with the provided payment ID',
        }),
        { status: 404 }
      )
    }

    // Check if payment is in a refundable state
    if (payment.status !== 'succeeded') {
      return new Response(
        JSON.stringify({
          error: 'Payment not refundable',
          message: 'Only succeeded payments can be refunded',
          payment_status: payment.status,
        }),
        { status: 400 }
      )
    }

    // Check if refund amount is valid
    const paymentAmount = parseFloat(payment.amount)
    if (numericAmount > paymentAmount) {
      return new Response(
        JSON.stringify({
          error: 'Invalid refund amount',
          message: 'Refund amount cannot exceed payment amount',
          payment_amount: paymentAmount,
          requested_amount: numericAmount,
        }),
        { status: 400 }
      )
    }

    const refundData = {
      amount: numericAmount,
      reason,
    }

    // Create refund through Zoho API
    const refund = await zohoPaymentService.createRefund(payment_id, refundData)

    // Update local transaction record with refund information
    const transaction = await zohoPaymentService.getTransaction(payment.payments_session_id)
    if (transaction) {
      transaction.refunds.push({
        refund_id: refund.refund_id,
        amount: numericAmount,
        status: refund.status,
        created_at: new Date(),
        reason,
      })
      await transaction.save()
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Refund created successfully',
        data: {
          refund_id: refund.refund_id,
          payment_id: payment_id,
          amount: refund.amount,
          currency: refund.currency,
          status: refund.status,
          reason: refund.reason,
          created_time: refund.created_time,
          reference_id,
        },
        refund: refund,
      }),
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating refund:', error.message)

    // Handle specific Zoho API errors
    if (error.message.includes('Zoho API Error')) {
      return new Response(
        JSON.stringify({
          error: 'Zoho Refund API Error',
          message: error.message,
          details: 'Please check your refund data and try again',
        }),
        { status: 400 }
      )
    }

    // Handle token errors
    if (error.message.includes('token')) {
      return new Response(
        JSON.stringify({
          error: 'Authentication Error',
          message: error.message,
          details: 'Please check your Zoho Payment configuration',
        }),
        { status: 401 }
      )
    }

    return new Response(
      JSON.stringify({
        error: 'Refund creation failed',
        message: error.message,
        details: 'An unexpected error occurred while creating the refund',
      }),
      { status: 500 }
    )
  }
}

/**
 * GET /api/zoho/refunds/create
 * Get refund creation requirements
 */
export async function GET() {
  const requirements = {
    message: 'Refund Creation Requirements',
    required_fields: [
      {
        field: 'payment_id',
        type: 'string',
        description: 'ID of the payment to refund',
        example: '173000002314883',
      },
      {
        field: 'amount',
        type: 'number',
        description: 'Refund amount (must be positive and <= payment amount)',
        example: 50.25,
      },
    ],
    optional_fields: [
      {
        field: 'reason',
        type: 'string',
        description: 'Reason for refund (max 500 characters, default: "Customer request")',
        example: 'Product returned by customer',
      },
      {
        field: 'reference_id',
        type: 'string',
        description: 'Your internal reference ID for the refund',
        example: 'REF-REFUND-123',
      },
    ],
    validation_rules: [
      'Payment must exist and be in "succeeded" status',
      'Refund amount must be positive',
      'Refund amount cannot exceed original payment amount',
      'Reason must be 500 characters or less',
    ],
    example_payload: {
      payment_id: '173000002314883',
      amount: 50.25,
      reason: 'Product returned by customer',
      reference_id: 'REF-REFUND-123',
    },
    supported_currencies: ['INR'],
    processing_time: '1-3 business days',
  }

  return new Response(JSON.stringify(requirements), { status: 200 })
}
