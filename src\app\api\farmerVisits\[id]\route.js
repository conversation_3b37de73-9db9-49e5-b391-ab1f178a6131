import connectedDB from '@/app/config/database'
import FarmerVisits from '@/app/models/FarmerVisits'

// GET /api/farmerVisits/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()

    // MongoDB aggregation pipeline to group by farmerName and project relevant fields
    const farmersList = await FarmerVisits.aggregate([
      {
        $match: {
          partnerId: params.id, // Filter by partnerId
        },
      },
      {
        $sort: {
          createdDateTime: -1, // Sort by visitedDate (createdDateTime) in descending order
        },
      },
      {
        $group: {
          _id: '$farmerName', // Group by farmerName
          visits: {
            $push: {
              createdDateTime: '$createdDateTime',
              doc: '$doc',
              farmSize: '$farmSize',
              pondId: '$pondId',
              farmType: '$farmType',
              location: '$location',
              mobileNumber: '$mobileNumber',
              speicesType: '$speicesType',
              productUsed: '$productUsed',
              harvestDirectly: '$harvestDirectly',
            },
          },
        },
      },
      {
        $project: {
          _id: 0, // Exclude the default _id field
          name: '$_id', // Rename _id to name
          visits: 1, // Include visits array
        },
      },
      {
        $sort: { name: 1 }, // Sort by farmerName in ascending order
      },
    ])

    // If no results, return an empty array
    if (!farmersList || farmersList.length === 0) {
      return new Response(JSON.stringify({ results: [] }), { status: 200 })
    }

    return new Response(JSON.stringify({ results: farmersList }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error.message, { status: 500 })
  }
}
