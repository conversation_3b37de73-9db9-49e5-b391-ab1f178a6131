# Invoices API

## Overview

The Invoices API provides access to invoice data for a specific customer, including detailed information about invoice items.

## Endpoint

```
GET /api/invoices/:customerId
```

## Parameters

| Parameter   | Type   | Required | Description                |
|-------------|--------|----------|----------------------------|
| customerId  | string | Yes      | The customer ID to retrieve invoices for |

## Response

### Success Response

**Status Code:** 200 OK

**Response Body:**
```json
{
  "results": [
    {
      "invoiceId": "INV123456",
      "invoiceDate": "2023-07-15T10:30:00.000Z",
      "invoiceNumber": "INV-2023-001",
      "invoiceStatus": "Paid",
      "addressId": "ADDR001",
      "customerId": "CUST001",
      "ageInDays": 45,
      "ageTier": "31-60 days",
      "subTotal": 10000,
      "total": 11800,
      "balance": 0,
      "deliveryMode": "Road Transport",
      "deliveryStatus": "Delivered",
      "dueDate": "2023-08-15T10:30:00.000Z",
      "items": [
        {
          "productId": "PROD001",
          "productName": "Aqua Feed - Growth Formula",
          "quantity": 5,
          "rate": 2000,
          "amount": 10000
        }
      ]
    }
  ]
}
```

### Error Response

**Status Code:** 500 Internal Server Error

**Response Body:**
```json
{
  "error": "Internal Server Error"
}
```

## Implementation Details

The API performs the following operations:

1. Connects to the database
2. Uses MongoDB aggregation to join invoice data with invoice items
3. Projects only the necessary fields
4. Sorts the results by invoice date in descending order (newest first)
5. Returns the combined result

## Data Models

The API uses the following MongoDB models:

### Invoices

Contains invoice records with the following fields:
- `invoiceId`: Unique identifier for the invoice
- `invoiceDate`: The date the invoice was created
- `invoiceNumber`: The human-readable invoice number
- `invoiceStatus`: The current status of the invoice (e.g., "Paid", "Unpaid", "Partially Paid")
- `addressId`: Reference to the delivery address
- `customerId`: The ID of the customer
- `ageInDays`: The age of the invoice in days
- `ageTier`: The age tier of the invoice (e.g., "0-30 days", "31-60 days")
- `subTotal`: The subtotal amount before taxes
- `total`: The total amount including taxes
- `balance`: The remaining balance to be paid
- `deliveryMode`: The mode of delivery
- `deliveryStatus`: The status of delivery
- `dueDate`: The due date for payment

### InvoiceItems

Contains invoice item records with the following fields:
- `invoiceId`: Reference to the parent invoice
- `productId`: The ID of the product
- `productName`: The name of the product
- `quantity`: The quantity purchased
- `rate`: The rate per unit
- `amount`: The total amount (quantity * rate)

## Example Usage

### Request

```
GET /api/invoices/CUST001
```

### Response

```json
{
  "results": [
    {
      "invoiceId": "INV789012",
      "invoiceDate": "2023-08-20T14:45:00.000Z",
      "invoiceNumber": "INV-2023-025",
      "invoiceStatus": "Unpaid",
      "addressId": "ADDR001",
      "customerId": "CUST001",
      "ageInDays": 10,
      "ageTier": "0-30 days",
      "subTotal": 15000,
      "total": 17700,
      "balance": 17700,
      "deliveryMode": "Road Transport",
      "deliveryStatus": "In Transit",
      "dueDate": "2023-09-20T14:45:00.000Z",
      "items": [
        {
          "productId": "PROD002",
          "productName": "Aqua Chemicals - Water Treatment",
          "quantity": 3,
          "rate": 5000,
          "amount": 15000
        }
      ]
    },
    {
      "invoiceId": "INV123456",
      "invoiceDate": "2023-07-15T10:30:00.000Z",
      "invoiceNumber": "INV-2023-001",
      "invoiceStatus": "Paid",
      "addressId": "ADDR001",
      "customerId": "CUST001",
      "ageInDays": 45,
      "ageTier": "31-60 days",
      "subTotal": 10000,
      "total": 11800,
      "balance": 0,
      "deliveryMode": "Road Transport",
      "deliveryStatus": "Delivered",
      "dueDate": "2023-08-15T10:30:00.000Z",
      "items": [
        {
          "productId": "PROD001",
          "productName": "Aqua Feed - Growth Formula",
          "quantity": 5,
          "rate": 2000,
          "amount": 10000
        }
      ]
    }
  ]
}
```

## Code Implementation

The implementation uses Next.js API routes with MongoDB for data storage. The API connects to the database and uses MongoDB's aggregation framework to join invoice data with invoice items.

Key features of the implementation:
- Uses MongoDB's `$lookup` to join invoice data with invoice items
- Projects only the necessary fields to reduce response size
- Sorts the results by invoice date in descending order
- Returns a combined result with invoice items nested within each invoice
