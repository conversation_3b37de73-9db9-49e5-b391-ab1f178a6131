import logo from '@/../public/images/Aquaconnect_logo.svg'
import { calculateOffer, indianCurrencyFormat } from '@/utils/formats'
import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import moment from 'moment'
import Image from 'next/image'

export function InvoiceDetailsNew({ customerInvoice, open, setOpen, customer }) {
  console.log('Customer : ', customer)

  return (
    <>
      <Dialog open={open} onClose={setOpen} className="relative z-10">
        <DialogBackdrop
          transition
          className="fixed inset-0 hidden bg-gray-200 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:block"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-stretch justify-center text-center md:items-center md:px-2 lg:px-4">
            {/* This element is to trick the browser into centering the modal contents. */}
            <span aria-hidden="true" className="hidden md:inline-block md:h-screen md:align-middle">
              &#8203;
            </span>
            <DialogPanel
              transition
              className="flex w-full transform text-left text-base transition data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:my-8 md:max-w-2xl md:px-4 data-[closed]:md:translate-y-0 data-[closed]:md:scale-95 lg:max-w-4xl"
            >
              <div className="relative flex w-full flex-col items-center overflow-hidden bg-white px-4 pb-8 pt-14 shadow-2xl sm:px-6 sm:pt-8 md:p-6 lg:p-8">
                <button
                  type="button"
                  onClick={() => setOpen(false)}
                  className="absolute right-4 top-4 text-gray-400 hover:text-gray-500 sm:right-6 sm:top-8 md:right-6 md:top-6 lg:right-8 lg:top-8"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon aria-hidden="true" className="h-6 w-6" />
                </button>
                <div className="flex h-screen w-full flex-1 flex-col">
                  <div className="-mx-4 px-4 pb-8 shadow-sm sm:mx-0 sm:rounded-lg sm:px-8 sm:pb-14 lg:col-span-2 lg:row-span-2 lg:row-end-2 xl:px-16 xl:pb-20">
                    <div className="flex flex-col items-start gap-x-6">
                      <Image src={logo} alt="Logo"></Image>
                      {/* <img
                        alt=""
                        src="https://tailwindui.com/img/logos/48x48/tuple.svg"
                        className="h-16 w-16 flex-none rounded-full ring-2 ring-gray-900/10"
                      /> */}
                      <h1>
                        {/* <div className="mt-1 text-base font-semibold leading-6 text-gray-900">
                          Coastal Aquaculture Research Institute Private Limited
                        </div> */}
                        <div className="text-sm leading-6 text-gray-500">
                          Invoice <span className="text-gray-700">#{customerInvoice.invoiceNumber}</span>
                        </div>
                      </h1>
                    </div>

                    {/* <h2 className="mt-5 text-base font-semibold leading-6 text-gray-900">Invoice</h2> */}
                    <dl className="mt-6 grid grid-cols-1 text-sm leading-6 sm:grid-cols-2">
                      <div className="sm:pr-4">
                        <dt className="inline text-gray-500">Issued on</dt>{' '}
                        <dd className="inline text-gray-700">
                          <time dateTime="2023-23-01">{moment(customerInvoice.invoiceDate).format('DD-MM-YYYY')}</time>
                        </dd>
                      </div>
                      <div className="mt-2 sm:mt-0 sm:pl-4">
                        <dt className="inline text-gray-500">{/* Due on */}</dt>{' '}
                        <dd className="inline text-gray-700">
                          {/* <time dateTime="2023-31-01">January 31, 2023</time> */}
                        </dd>
                      </div>
                      <div className="mt-6 border-t border-gray-900/5 pt-6 sm:pr-4">
                        <dt className="font-semibold text-gray-900">From</dt>
                        <dd className="mt-2 text-gray-500">
                          <span className="font-medium text-gray-900">
                            {' '}
                            Coastal Aquaculture Research Institute Private Limited
                          </span>
                          <br />
                          Type II/17, Dr.VSI Estate, Thiruvanmiyur, Chennai, Tamilnadu - 600041
                        </dd>
                      </div>
                      <div className="mt-8 sm:mt-6 sm:border-t sm:border-gray-900/5 sm:pl-4 sm:pt-6">
                        <dt className="font-semibold text-gray-900">To</dt>
                        <dd className="mt-2 text-gray-500">
                          <span className="font-medium text-gray-900">{customer.companyName}</span>
                          <br />
                          {customer.billingAddress}
                          <br />
                          GST NO: {customer.gstNo}
                        </dd>
                      </div>
                    </dl>
                    <table className="mt-16 w-full whitespace-nowrap text-left text-sm leading-6">
                      <colgroup>
                        <col className="w-full" />
                        <col />
                        <col />
                        <col />
                      </colgroup>
                      <thead className="border-b border-gray-200 text-gray-900">
                        <tr>
                          <th scope="col" className="px-0 py-3 font-semibold">
                            Item
                          </th>
                          <th scope="col" className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell">
                            Qty
                          </th>
                          <th scope="col" className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell">
                            Price
                          </th>
                          <th scope="col" className="py-3 pl-8 pr-0 text-right font-semibold">
                            Discount
                          </th>
                          <th scope="col" className="py-3 pl-8 pr-0 text-right font-semibold">
                            Total
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {customerInvoice.items.map((details, index) => (
                          <tr key={index} className="border-b border-gray-100">
                            <td className="max-w-0 px-0 py-5 align-top">
                              <div className="truncate font-medium text-gray-900">{details.itemName}</div>
                              <div className="truncate text-gray-500">{indianCurrencyFormat(details.itemPrice)}</div>
                            </td>
                            <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
                              {details.quantity}
                            </td>
                            <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
                              {indianCurrencyFormat(details.subTotal)}
                            </td>
                            <td className="py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700">
                              {calculateOffer(details.subTotal, details.total)}%
                            </td>
                            <td className="py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700">
                              {indianCurrencyFormat(details.total)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <th scope="row" className="px-0 pb-0 pt-6 font-normal text-gray-700 sm:hidden">
                            Subtotal
                          </th>
                          <th
                            scope="row"
                            colSpan={4}
                            className="hidden px-0 pb-0 pt-6 text-right font-normal text-gray-700 sm:table-cell"
                          >
                            Subtotal
                          </th>
                          <td className="pb-0 pl-8 pr-0 pt-6 text-right tabular-nums text-gray-900">
                            {indianCurrencyFormat(customerInvoice.subTotal)}
                          </td>
                        </tr>
                        <tr>
                          <th scope="row" className="pt-4 font-normal text-gray-700 sm:hidden">
                            Tax
                          </th>
                          <th
                            scope="row"
                            colSpan={4}
                            className="hidden pt-4 text-right font-normal text-gray-700 sm:table-cell"
                          >
                            Tax
                          </th>
                          <td className="pb-0 pl-8 pr-0 pt-4 text-right tabular-nums text-gray-900">
                            {indianCurrencyFormat((customerInvoice.total - customerInvoice.subTotal).toFixed(2))}
                          </td>
                        </tr>
                        <tr>
                          <th scope="row" className="pt-4 font-semibold text-gray-900 sm:hidden">
                            Total
                          </th>
                          <th
                            scope="row"
                            colSpan={4}
                            className="hidden pt-4 text-right font-semibold text-gray-900 sm:table-cell"
                          >
                            Total
                          </th>
                          <td className="pb-0 pl-8 pr-0 pt-4 text-right font-semibold tabular-nums text-gray-900">
                            {indianCurrencyFormat(customerInvoice.total)}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  )
}
