import React from 'react'

interface ProductCardsProps {
  productImage: string
  productName: string
}

export const ProductCards: React.FC<ProductCardsProps> = ({ productImage, productName }) => {
  return (
    // <div className="overflow-hidden f rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:p-6">
    //   <div className="items-center lg:flex">
    //     <div className="aspect-auto relative h-56 w-full overflow-hidden rounded lg:w-44">
    //       <img className="h-auto max-h-full w-full dark:hidden" src={productImage} alt={`${productName} image`} />
    //       <img className="hidden h-auto max-h-full w-full dark:block" src={productImage} alt={`${productName} image`} />
    //     </div>

    //     <div className="flex-1 pt-4 lg:pl-6 lg:pt-0">
    //       <a href="#" className="text-xl font-semibold text-gray-900 hover:underline dark:text-white">
    //         {productName}
    //       </a>
    //     </div>
    //   </div>
    // </div>
    <div className="flex flex-col items-center justify-center rounded-lg bg-white">
      <div className="left-img">
        <img className="object-fit h-3/4 w-full dark:hidden" src={productImage} alt={`${productName} image`} />
      </div>
      <div className="rgt-cont">
        <a href="#" className="text-xl font-semibold text-gray-900 hover:underline dark:text-white">
          {productName}
        </a>
      </div>
    </div>
  )
}
