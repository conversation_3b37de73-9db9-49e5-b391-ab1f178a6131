'use client';
import React from 'react';
import { Line } from 'react-chartjs-2';

import {
  Chart as ChartJS,
  CategoryScale,
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  Title,
  Tooltip,
  Legend,
  plugins,
  elements,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  Title,
  Tooltip,
  Legend
);

import { lineChartData } from '@/chartData';

const LineChart = () => {
  const options = {
    responsive: true,
    tension: 0,
    borderColor: 'rgba(127, 86, 217, 1)',
    borderDash: [5, 5],
    elements: {
      point: {
        pointStyle: false,
      },
    },
    plugins: {
      legend: {
        display: false,
      },
    },
  };
  const data = {};
  return <Line options={options} data={lineChartData} />;
};

export default LineChart;
