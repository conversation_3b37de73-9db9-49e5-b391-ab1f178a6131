import logo from '@/../public/images/Aquaconnect_logo.svg'
import { Badge } from '@/components/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'

import { calculateDiscountedPrice, calculateOffer, indianCurrencyFormat } from '@/utils/formats'
import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import moment from 'moment'
import Image from 'next/image'

function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

export function SalesOrderDetailsNew({ orderDetails, open, setOpen, customer }) {

  return (
    <>
      <Dialog open={open} onClose={setOpen} className="relative z-10">
        <DialogBackdrop
          transition
          className="fixed inset-0 hidden bg-gray-200 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:block"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-stretch justify-center text-center md:items-center md:px-2 lg:px-4">
            {/* This element is to trick the browser into centering the modal contents. */}
            <span aria-hidden="true" className="hidden md:inline-block md:h-screen md:align-middle">
              &#8203;
            </span>
            <DialogPanel
              transition
              className="flex w-full transform text-left text-base transition data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:my-8 md:max-w-2xl md:px-4 data-[closed]:md:translate-y-0 data-[closed]:md:scale-95 lg:max-w-4xl"
            >
              <div className="relative flex w-full flex-col items-center overflow-hidden bg-white px-4 pb-8 pt-14 shadow-2xl sm:px-6 sm:pt-8 md:p-6 lg:p-8">
                <button
                  type="button"
                  onClick={() => setOpen(false)}
                  className="absolute right-4 top-4 text-gray-400 hover:text-gray-500 sm:right-6 sm:top-8 md:right-6 md:top-6 lg:right-8 lg:top-8"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon aria-hidden="true" className="h-6 w-6" />
                </button>
                <div className="flex h-screen w-full flex-1 flex-col">
                  <div className="-mx-4 px-4 pb-8 shadow-sm sm:mx-0 sm:rounded-lg sm:px-8 sm:pb-14 lg:col-span-2 lg:row-span-2 lg:row-end-2 xl:px-16 xl:pb-20">
                    <div className="flex items-start gap-x-6">
                      <div className="mb-5 font-bold dark:text-white sm:mb-0">#{orderDetails.salesOrderNumber}</div>
                      <div className="space-y-3 text-left sm:ml-auto sm:text-right">
                        <div className="flex justify-end">
                          <Image src={logo} alt="Aquaconnect Logo" />
                        </div>
                        <div className="space-y-1">
                          <div className="text-lg font-semibold text-gray-900 dark:text-white">
                            Coastal Aquaculture Research Institute Private Limited
                          </div>
                          <div className="text-sm font-normal text-gray-900 dark:text-white">
                            Type II/17, Dr.VSI Estate, Thiruvanmiyur, Chennai, Tamilnadu - 600041
                          </div>
                        </div>
                        <div className="text-sm font-normal text-gray-500 dark:text-gray-400">
                          {moment(orderDetails.createdTime).format('DD MMM, YYYY')}
                        </div>
                        <div className="text-sm font-normal text-gray-500 dark:text-gray-400">
                          <Badge>{orderDetails.invoicedStatus}</Badge>
                        </div>
                      </div>
                    </div>
                    <div className="sm:w-72">
                      <div className="mb-4 text-base font-bold uppercase text-gray-900 dark:text-white">
                        Delivery Address:
                      </div>
                      <address className="text-base font-normal text-gray-500 dark:text-gray-400">
                        {customer.companyName}
                        <br />
                        {customer.billingAddress}
                      </address>
                    </div>
                    <div className="my-8 flex flex-col">
                      <div className="overflow-x-auto border-b border-gray-200 dark:border-gray-600">
                        <div className="inline-block min-w-full align-middle">
                          <div className="overflow-hidden shadow">
                            <Table className="mt-8 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
                              <TableHead>
                                <TableRow>
                                  <TableHeader>Item</TableHeader>
                                  <TableHeader>Item Price</TableHeader>
                                  <TableHeader>Discount</TableHeader>
                                  <TableHeader>Sub Total</TableHeader>
                                  <TableHeader>Total</TableHeader>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {orderDetails.items.map((details, index) => (
                                  <TableRow key={index}>
                                    <TableCell className="whitespace-nowrap p-4 text-sm font-normal">
                                      <div className="text-base font-semibold text-gray-900 dark:text-white">
                                        {details.itemName}
                                      </div>
                                    </TableCell>
                                    <TableCell className="whitespace-nowrap p-4 text-base font-normal text-gray-500 dark:text-gray-400">
                                      {indianCurrencyFormat(details.itemPrice)}
                                    </TableCell>
                                    <TableCell className="whitespace-nowrap p-4 text-base font-normal text-gray-500 dark:text-gray-400">
                                      {details.entityDiscountPercent}
                                    </TableCell>
                                    <TableCell className="whitespace-nowrap p-4 text-base font-semibold text-gray-900 dark:text-white">
                                      {indianCurrencyFormat(details.total)}
                                    </TableCell>
                                    <TableCell className="whitespace-nowrap p-4 text-base font-semibold text-gray-900 dark:text-white">
                                      {indianCurrencyFormat(
                                        calculateDiscountedPrice(details.total, details.entityDiscountPercent).toFixed(
                                          2
                                        )
                                      )}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3 sm:ml-auto sm:w-72 sm:text-right">
                      <div className="flex justify-between">
                        <div className="text-sm font-medium uppercase text-gray-500 dark:text-gray-400">Price</div>
                        <div className="text-base font-medium text-gray-900 dark:text-white">
                          {indianCurrencyFormat(orderDetails.subTotal)}
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <div className="text-sm font-medium uppercase text-gray-500 dark:text-gray-400">Tax rate</div>
                        <div className="text-base font-medium text-gray-900 dark:text-white">
                          {calculateOffer(orderDetails.total, orderDetails.subTotal)}%
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <div className="text-sm font-medium uppercase text-gray-500 dark:text-gray-400">Discount</div>
                        <div className="text-base font-medium text-gray-900 dark:text-white">
                          {calculateOffer(orderDetails.subTotal, orderDetails.total).toFixed(2)}
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <div className="text-base font-semibold uppercase text-gray-900 dark:text-white">Total</div>
                        <div className="text-base font-bold text-gray-900 dark:text-white">
                          {indianCurrencyFormat(orderDetails.total)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  )
}
