import { Schema, model, models } from 'mongoose'
import moment from 'moment'
import 'moment-timezone'

const OpeningBalanceSchema = new Schema(
  {
    customerId: String,
    customerCode: String,
    openingBalance: Number,
    date: String, // Keeping as string for backward compatibility
    updatedTime: Date,
  },
  {
    collection: 'OpeningBalance',
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
)

// Add a virtual for formatted date to help with timezone-aware queries
OpeningBalanceSchema.virtual('formattedDate').get(function() {
  if (!this.date) return null;
  return moment.tz(this.date, 'DD-MMM-YYYY', 'Asia/Kolkata').format('DD-MMM-YYYY');
});

// Add a virtual for date as ISO string
OpeningBalanceSchema.virtual('dateISO').get(function() {
  if (!this.date) return null;
  return moment.tz(this.date, 'DD-MMM-YYYY', 'Asia/Kolkata').toISOString();
});

const OpeningBalance = models.OpeningBalance || model('OpeningBalance', OpeningBalanceSchema)

export default OpeningBalance
