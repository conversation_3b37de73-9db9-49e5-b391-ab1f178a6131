'use client'

import { Badge } from '@/components/badge'
import { Select } from '@/components/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { useFetchSMRReportByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useEffect, useState } from 'react'
const InventoryPage = () => {
  const [loading, setLoading] = useState(true)
  const buttonList = [
    { label: 'All', value: 'all' },
    { label: 'Non-Moving', value: 'Non-Moving' },
    { label: 'To be corrected', value: 'To be corrected' },
    { label: 'Moving', value: 'Moving' },
  ]
  const customer = useAtomValue(retailerAtom)
  const { smrReport, modifiedSmrReport, setModifiedSmrReport } = useFetchSMRReportByCustomerId(customer.customerId)

  if (smrReport) {
    console.log('REport Date', smrReport)
  }
  useEffect(() => {
    if (smrReport) {
      setLoading((prev) => false)
    }
  }, [smrReport])
  const onFilterChange = (e) => {
    console.log('selectedOption ', e.target.value)

    if (e.target.value == 'all') {
      setModifiedSmrReport(smrReport)
      return
    }

    setModifiedSmrReport(smrReport.filter((x) => x.status == e.target.value))
  }

  return (
    <>
      {loading && <LoadingScreen />}
      <div className="pb-6 mt-2  align-baseline sm:flex sm:justify-between">
        <div className="flex flex-col sm:flex-row">
          <p className="text-2xl font-bold">Stocks</p>
        </div>
      </div>
      <div className="flex flex-col justify-between lg:flex-row">
        <h2 className="text-xl lg:flex lg:items-center">
          <span className="text-gray-400">
            As of {smrReport && moment(smrReport[0]?.lastDate).utc().format('DD-MM-YYYY')}
          </span>
        </h2>
        <div className="mt-2 w-full sm:mt-0 sm:w-auto lg:w-52">
          <Select onChange={onFilterChange} name="accountStatement">
            {buttonList.map(({ label, value }) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </Select>
        </div>
      </div>

      <Table>
        <TableHead>
          <TableRow>
            <TableHeader className="h-16 w-1/3">Product Name</TableHeader>
            <TableHeader className="w-1/5 max-sm:hidden">Status</TableHeader>

            <TableHeader className="text-right">Quantity</TableHeader>
            {/* <TableHeader>Sold</TableHeader> */}
            <TableHeader className="text-right">
              Sales ({smrReport && smrReport[0] && moment(smrReport[0]?.startDate).utc().format('DD MMM')} -{' '}
              {smrReport && smrReport[0] && moment(smrReport[0]?.lastDate).utc().format('DD MMM')})
              <br />
              {/* Activity between{' '}
              {report.results &&
                moment(report.results[0].lastDate).subtract({ hour: 5, minutes: 30 }).format('DD-MM-YYYY')}{' '}
              -{' '}
              {report.results &&
                moment(report.results[0].firstDate).subtract({ hour: 5, minutes: 30 }).format('DD-MM-YYYY')}
              <br /> */}
            </TableHeader>
          </TableRow>
        </TableHead>
        <TableBody>
          {modifiedSmrReport &&
            modifiedSmrReport.map((report, index) => {
              return (
                <TableRow
                  key={index}
                  // className={` ${index % 2 === 0 && 'bg-slate-100'}`}
                >
                  <TableCell>{`${report.productName}`}</TableCell>
                  <TableCell className="max-sm:hidden">
                    <Badge color={report.status == 'Moving' ? 'green' : 'red'}>{`${report.status}`}</Badge>
                  </TableCell>

                  {/* <TableCell>{`${report.closingBalance}`}</TableCell> */}
                  {/* {
                    `${report.sales} ${report.srn} ${report.invoice}`
                    } */}
                  <TableCell className="text-right">{`${report.closingBalance}`}</TableCell>
                  <TableCell className="align-top">
                    <div className="flex flex-row gap-3">
                      {/* <div className="w-20">{report.openingBalance}</div> */}
                      {/* <div className="w-10">{report.invoice}</div> */}
                      <div className="w-full text-right">{report.sales}</div>
                      {/* <div className="w-14">{report.srn}</div> */}
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
        </TableBody>
      </Table>
    </>
  )
}

export default InventoryPage
