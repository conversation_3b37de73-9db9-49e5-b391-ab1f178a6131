'use server'
import connectedDB from '@/app/config/database'
import PaymentAccessToken from '@/app/models/PaymentAccessToken'

// api/zohoPayment?amount=10.00&invoice=sdff123

export const GET = async (request, { params }) => {
  try {
    const params = new URL(request.url).searchParams
    console.log('parameter ', params)

    await connectedDB()
    const result = await PaymentAccessToken.findOne({})

    const paymentSessionResponse = await fetch(
      `${process.env.ZOHO_PAYMENT_SESSION_URL}?account_id=${process.env.ZOHO_PAY_ACCOUNT_ID}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Zoho-oauthtoken ${result.access_token}`,
        },
        body: JSON.stringify({
          amount: params.amount,
          currency: 'INR',
          meta_data: [
            {
              key: 'invoice_no',
              value: params.invoice,
            },
          ],
        }),
      }
    )

    console.log(paymentSessionResponse)

    return new Response(JSON.stringify({ results: 'Success' }), { status: 200 })
  } catch (e) {
    console.log(e)
    return new Response(JSON.stringify({ error: 'try again later' }), { status: 201 })
  }
}
