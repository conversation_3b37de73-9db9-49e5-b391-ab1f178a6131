import { Schema, model, models } from 'mongoose';

const SalesMasterDataSchema = new Schema(
  {
    invoiceId: String,
    invoiceNumber: String,
    invoiceDate: String,
    customerId: String,
    customerCode: String,
    customerName: String,
    customerDistrict: String,
    customerState: String,
    customerType: String,
    onBoardedTime: Date,
    categoryType: String,
    invoiceType: String,
    retailType: String,
    invoiceStatus: String,
    invoiceRaisedBy: String,
    mode: String,
    businessVertical: String,
    feedCreditLimit: Number,
    nonFeedCreditLimit: Number,
    harvestCreditLimit: Number,
    totalSalesInclTax: Number,
    totalSalesExclTax: Number,
    tcsAmount: Number,
    tdsAmount: Number,
    amountAfterTcs: Number,
    shippingCharges: Number,
    feedAmountAfterTcs: Number,
    nonFeedAmountAfterTcs: Number,
    creditNoteAmountWithTcs: Number,
    payableAmount: Number,
    paidAmount: Number,
    due: Number,
    dueDate: Date,
    dueDays: Number,
    aging: String,
    aging1: String,
    maxPaymentDate: Number,
    minPaymentDate: Number,
    avgPaymentDate: Number,
    numberOfPayments: Number,
    paymentCredibility: Number,
    totalPurchase: Number,
    totalSales: Number,
    salesTier: Number,
    healthcareSales: Number,
    feedSales: Number,
    chemicalSales: Number,
    equipmentSales: Number,
    harvestSales: Number,
    grossMargin: Number,
    grossMarginOfFeed: Number,
    grossMarginOfNonfeed: Number,
    cnAmount: Number,
    cnPurchasePrice: Number,
    cnPurchasePriceFeed: Number,
    cnPurchasePriceNonfeed: Number,
    grossMarginOfCn: Number,
    grossMarginOfCnForFeed: Number,
    grossMarginOfCnForNonfeed: Number,
    grossMarginAfterCn: Number,
    grossMarginAfterCnForFeed: Number,
    grossMarginAfterCnForNonfeed: Number,
    healthcareCn: Number,
    healthcareSalesAfterCn: Number,
    feedCn: Number,
    feedSalesAfterCn: Number,
    chemicalCn: Number,
    chemicalSalesAfterCn: Number,
    equipmentCn: Number,
    equipmentSalesAfterCn: Number,
    harvestCn: Number,
    harvestSalesAfterCn: Number,
    nonfeedCn: Number,
    invoiceDateDiff: Number,
    lastPaidDate: String,
    submissionDate: String,
  },
  { collection: 'EntireSalesMaster' }
);

const SalesMasterData =
  models.EntireSalesMaster || model('EntireSalesMaster', SalesMasterDataSchema);

export default SalesMasterData;
