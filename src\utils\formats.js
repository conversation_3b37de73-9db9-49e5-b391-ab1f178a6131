const indianCurrencyFormat = (amount, decimal = 2) => {
  if (typeof amount === 'string') {
    amount = amount.replace(/[^0-9.-]/g, '') // Remove non-numeric characters
  }

  amount = parseFloat(amount) // Convert to a number

  if (isNaN(amount)) {
    return '₹0' // Return a default value if the amount is not a valid number
  }

  const amountInRupees = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: decimal,
    maximumFractionDigits: decimal,
  }).format(amount)

  return amountInRupees
}

const calculateOffer = (subTotal, total) => {
  if (typeof subTotal === 'string') {
    subTotal = subTotal.replace(/[^0-9.]/g, '')
  }
  if (typeof total === 'string') {
    total = total.replace(/[^0-9.]/g, '')
  }

  var offer = Math.round((1 - total / subTotal) * 100)
  return isNaN(offer) ? 0 : offer
}

const convertToNumber = (val) => {
  if (typeof val === 'string') {
    val = val.replace(/[^0-9.]/g, '')
  }

  return val
}

const calculateDiscountedPrice = (subTotal, discount) => {
  if (typeof subTotal === 'string') {
    subTotal = subTotal.replace(/[^0-9.]/g, '')
  }
  if (typeof discount === 'string') {
    discount = discount.replace(/[^0-9.]/g, '')
  }

  var discount = subTotal * (1 - discount / 100)

  return isNaN(discount) ? 0 : discount
}

// Function to remove currency symbols and commas, and convert to number
const parseCurrency = (total) => {
  return parseFloat(total.replace(/INR\s|,/g, ''))
}

function calculatePercentage(value, percentage) {
  var percentage = (value * percentage) / 100
  return isNaN(percentage) ? 0 : percentage
}

function findPercentageOfNumber(part, total) {
  var percentage = (part / total) * 100
  return isNaN(percentage) ? 0 : percentage
}

const currencyFormat = (amount, decimal = 2) => {
  if (typeof amount === 'string') {
    amount = amount.replace(/[^0-9.]/g, '')
  }

  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: decimal,
    maximumFractionDigits: decimal,
  }).format(amount)
}
export {
  calculateDiscountedPrice,
  calculateOffer,
  calculatePercentage,
  convertToNumber,
  currencyFormat,
  findPercentageOfNumber,
  indianCurrencyFormat,
  parseCurrency,
}
