//import connectedDB from '@/config/database';
import connectedDB from '@/app/config/database'
import SMRReport from '@/app/models/SMRReport'
// GET /api/smrReport/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const smrReport = await SMRReport.find({
      partnerId: params.customerId,
    })

    if (!smrReport) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify({ results: smrReport }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
