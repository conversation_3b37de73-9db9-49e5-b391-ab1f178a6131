'use client'
import { useEffect, useState } from 'react'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import { calculatePercentage, findPercentageOfNumber, indianCurrencyFormat } from '@/utils/formats'
import { useFetchScheme, useFetchSupportPersionsData } from '@/utils/requests'
import { Button } from '@headlessui/react'
import { useAtomValue } from 'jotai'
import { ContactCard } from './ContactCard'
import { EligileBrandsModal } from './EligibleBrandsModal'
import { retailerAtom } from './providers'

const createVoucherTier = (tier, threshold, payload, drGrowPercentageFromTotal) => ({
  tier,
  threshold,
  drGrowThreshold() {
    return calculatePercentage(this.threshold, drGrowPercentageFromTotal)
  },

  otherThreshold() {
    return this.threshold - this.drGrowThreshold()
  },

  progressPercentage() {
    const percentageDrGrow = findPercentageOfNumber(payload.drGrow, this.drGrowThreshold())
    const percentageTotal = findPercentageOfNumber(payload.total, this.threshold)

    return Math.min(percentageDrGrow, percentageTotal, 100)
  },
})

const RewardScheme = () => {
  const [openEligibleProducts, setOpenEligibleProducts] = useState(false)
  const [ticketWon, setTicketWon] = useState('')
  const customer = useAtomValue(retailerAtom)
  const { results: payload } = useFetchScheme(customer.customerId)
  let supportPersionsDetails = useFetchSupportPersionsData(customer.customerId)

  const brandsList = [
    'Dr.GrowTea Seed Powder*AC-3902-IMP  $ Upto 50% $ brand',
    'Aqua C+*AC-5600 $ Upto 50% $ brand',
    'Aqua K+*AC-5601 $ Upto 50% $ brand',
  ]

  useEffect(() => {
    if (!payload) return
    if (payload.total >= 600000 && payload.drGrowSales >= 120000 && payload.payment >= 600000) setTicketWon('1')
    else if (payload.total >= 1200000 && payload.drGrowSales >= 240000 && payload.payment >= 1200000)
      setTicketWon('1 + 2')
    else if (payload.total >= 1800000 && payload.drGrowSales >= 360000 && payload.payment >= 1800000)
      setTicketWon('1 + 2 + 3')
    else setTicketWon('')
  }, [payload])

  return (
    <div className="mb-15 mx-auto rounded-lg bg-white">
      <div className="flex h-28 w-full bg-scheme-pattern-mobile bg-no-repeat md:bg-scheme-pattern-tab lg:bg-scheme-pattern"></div>

      <div className="mb-16 flex flex-col lg:flex-row">
        <div className="grow lg:mb-0 lg:mr-2 lg:mt-0 lg:w-1/2">
          <div className="w-full gap-4 rounded-lg border border-gray-200 bg-gray-50 p-6">
            <p className="mb-4 text-lg font-semibold capitalize leading-8 text-[#4B5563]">
              <span className="font-bold text-blue-600"> Fly to Phuket, Thailand</span>
              <span className="font-inter text-lg font-normal text-blue-600"> by completing the sales numbers.</span>

              <span className="font-inter text-lg font-normal">
                {' '}
                Talk to your Aquaconnect Officer, Brahmam 6382077432 to know more.{' '}
              </span>
            </p>
            <Table className="mb-3 rounded-lg border border-gray-200">
              <TableHead className="bg-[#F9FAFB]">
                <TableRow>
                  <TableHeader></TableHeader>
                  <TableHeader>1 Ticket</TableHeader>
                  <TableHeader>2 Tickets</TableHeader>
                  <TableHeader>3 Tickets</TableHeader>
                </TableRow>
              </TableHead>

              <TableBody>
                <TableRow className="justify-end px-4 text-sm font-normal text-[#6B7280]">
                  <TableCell className="font-medium text-[#111827]">
                    <span className="ml-3">Total</span>
                  </TableCell>
                  <TableCell>{indianCurrencyFormat(600000, 0)}</TableCell>
                  <TableCell>{indianCurrencyFormat(1200000, 0)}</TableCell>
                  <TableCell>{indianCurrencyFormat(1800000, 0)}</TableCell>
                </TableRow>
                <TableRow className="text-sm font-normal text-[#6B7280]">
                  <TableCell className="font-medium text-[#111827]">
                    <span className="ml-3">Dr Grow (Min)</span>
                  </TableCell>
                  <TableCell>{indianCurrencyFormat(120000, 0)}</TableCell>
                  <TableCell>{indianCurrencyFormat(240000, 0)}</TableCell>
                  <TableCell>
                    <span className="mr-3">{indianCurrencyFormat(360000, 0)}</span>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div className="mt-2 flex flex-row items-center pl-2">
              <Button
                className="inline-flex items-center justify-center pr-2 text-sm font-medium text-[#1644CE]"
                onClick={() => setOpenEligibleProducts(true)}
              >
                Eligible Products
              </Button>
            </div>
          </div>
        </div>
        <div className="mt-2 grow flex-col rounded-md border border-zinc-300 bg-gray-50 p-6 sm:mt-0 lg:ml-2 lg:w-1/2">
          {payload ? (
            typeof payload === 'object' && payload?.customerId ? (
              <>
                <div className="mb-4">
                  <div className="mb-2 font-inter text-[14px] text-sm font-normal capitalize leading-[24px] text-[#4B5563]">
                    <p className="pb-2 font-inter text-lg font-semibold uppercase leading-tight text-[#111827]">
                      <span className="font-bold">Your Performance</span>
                    </p>
                  </div>

                  <div className={`flex text-sm font-normal`}>
                    <p className="text-left text-[#6B7280]">Total Value</p>
                    <p className="grow text-right font-medium">{indianCurrencyFormat(payload.total)}</p>
                  </div>
                  <div className={`flex py-3 text-sm font-normal`}>
                    <p className="w-24 text-left text-[#6B7280]">Dr Grow</p>
                    <p className="grow text-right font-medium">{indianCurrencyFormat(payload.drGrowSales)}</p>
                  </div>
                  <div className={`flex text-sm font-normal`}>
                    <p className="w-24 text-left text-[#6B7280]">Payment</p>
                    <p className="grow text-right font-medium">{indianCurrencyFormat(payload.payment)}</p>
                  </div>
                </div>
                <div className="">
                  {typeof payload === 'object' && payload?.customerId && <WonRewards wonVoucher={ticketWon} />}
                </div>
              </>
            ) : (
              <>
                <UserConsent supportPersionsDetails={supportPersionsDetails} />
              </>
            )
          ) : (
            <></>
          )}
        </div>
      </div>

      {openEligibleProducts && (
        <EligileBrandsModal open={openEligibleProducts} setOpen={setOpenEligibleProducts} brandsList={brandsList} />
      )}
    </div>
  )
}

const UserConsent = ({ supportPersionsDetails }) => (
  <div
    className="max-w-full rounded-xl bg-gray-50 px-4 py-6 pt-2 sm:pt-0"
    role="alert"
    tabIndex="-1"
    aria-labelledby="hs-toast-stack-toggle-label"
  >
    <p id="hs-toast-stack-toggle-label" className="text-sm font-medium text-[#000000] dark:text-white">
      You are yet to start winning
    </p>
    <div className="mt-1 text-sm font-normal text-[#6B7280] dark:text-neutral-400">
      Talk to your Aquaconnect officer to more about the scheme if you have any question.
    </div>

    {supportPersionsDetails && supportPersionsDetails.results && (
      <ContactCard person={supportPersionsDetails.results.filter((val) => val.userProfile === 'SO')[0]} />
    )}

    {/* <div className="flex items-center pt-6">
      <UserCircleIcon className="size-12 text-[#6B7280]" />
      <div className="ms-4 min-w-0 flex-1">
        <p className="truncate text-sm font-medium text-[#111827] dark:text-white">Neil Sims</p>
        <p className="truncate text-sm font-normal text-[#6B7280] dark:text-gray-400">+91 9876543210</p>
      </div>
    </div> */}
  </div>
)
const WonRewards = ({ wonVoucher }) => {
  const voucher = wonVoucher ? wonVoucher.split('+').map((each) => each.trim().toLowerCase()) : []

  return (
    <div>
      {/* Count of vouchers */}
      <div className="mb-2 font-inter text-[12px] text-sm font-normal capitalize text-[#4B5563]">
        <p className="pb-2 font-inter text-lg text-[#111827]">Tickets won: {voucher.length}</p>
      </div>

      {/* List of RewardComponent elements */}
      <div className="flex space-x-4">
        {voucher.map((eachCoupon, index) => (
          <RewardComponent key={index} couponType={eachCoupon} />
        ))}
      </div>
    </div>
  )
}

const RewardComponent = ({ couponType }) => {
  let price
  let couponGradient

  switch (couponType) {
    case 'bronze':
      price = '10,000'
      couponGradient = 'bg-bronze-coupon-gradient'
      break
    case 'silver':
      price = '25,000'
      couponGradient = 'bg-silver-coupon-gradient'
      break
    case 'gold':
      price = '60,000'
      couponGradient = 'bg-gold-coupon-gradient'
      break
  }

  return (
    <div className="h-[65px] w-[140px]">
      <img src="./images/Reward-Ticket.jpg" alt="Reward-Image" className="h-full w-full object-cover" />
    </div>
  )
}

const TierTab = ({ name, voucherAmount, activeTab, onTabChange }) => (
  <li onClick={() => onTabChange(name)}>
    <a
      className={`inline-block rounded-t-lg border-b-2 p-4 capitalize ${
        activeTab === name
          ? 'border-[#4F46E5] text-[#4F46E5]'
          : 'border-transparent hover:border-gray-300 hover:text-gray-600 dark:hover:text-gray-300'
      }`}
    >
      {name}
      <p className="text-xs font-normal text-[#6B7280]">Gift Voucher {voucherAmount}</p>
    </a>
  </li>
)
export default RewardScheme
