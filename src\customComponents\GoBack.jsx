import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ChevronLeftIcon } from '@heroicons/react/24/solid';

const GoBack = () => {
  const router = useRouter();

  return (
    <Link
      href="/"
      onClick={() => router.back()}
      className="inline-flex items-center gap-2 text-sm/6 text-zinc-500 dark:text-zinc-400">
      <ChevronLeftIcon className="size-4 fill-zinc-400 dark:fill-zinc-500" />
      Go Back
    </Link>
  );
};

export default GoBack;
