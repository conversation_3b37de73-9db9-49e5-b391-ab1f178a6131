import {
  carouselDataAtom,
  customerPaymentsAtom,
  customerStatementAtom,
  dashboardAtom,
  duesAtom,
  farmerVisitsAtom,
  invoicesAtom,
  priceListAtom,
  productCatalogueAtom,
  salesMasterDataAtom,
  salesOrdersAtom,
  schemeDataAtom,
  smrReportAtom,
  supportPersionsAtom,
} from '@/customComponents/providers'
import { useAtom } from 'jotai'
import { useEffect, useState } from 'react'
const apiDomain = '/api' || null

// Utility function for fetching data
async function _fetchData(url) {
  const response = await fetch(url, { next: { revalidate: 3600 } })
  if (!response.ok) {
    throw new Error('Failed to fetch data!', response, url)
  }
  const json_response = await response.json()
  return json_response
}

// Example React hook for using invoices in a component
async function validateRetailer(phno) {
  console.log('phno : ', phno)
  if (!apiDomain || !phno) return []
  const url = `${apiDomain}/validateRetailer/${phno}`
  const retailer = await _fetchData(url)

  return retailer
}

// Example React hook for using invoices in a component
function useFetchSupportPersionsData(customerId) {
  const [supportPersions, setSupportPersions] = useAtom(supportPersionsAtom)
  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/getUserDetails/${customerId}`
      const supportPersions = await _fetchData(url)
      setSupportPersions(supportPersions)
    }

    fetchData()
  }, [customerId, setSupportPersions])

  return supportPersions
}

function useFetchPriceListData(customerId) {
  const [priceList, setPriceList] = useAtom(priceListAtom)
  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/priceList/${customerId}`
      const priceList = await _fetchData(url)
      setPriceList(priceList)
    }

    fetchData()
  }, [customerId, setPriceList])

  return priceList
}

// Example React hook for using invoices in a component
function useFetchInvoicesByCustomerId(customerId) {
  const [invoices, setInvoices] = useAtom(invoicesAtom)
  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/invoices/${customerId}`
      const invoices = await _fetchData(url)

      setInvoices(invoices)
    }

    fetchData()
  }, [customerId, setInvoices])

  return invoices
}

// Example React hook for using sales orders in a component
function useFetchSalesOrdersByCustomerId(customerId) {
  const [salesOrders, setSalesOrders] = useAtom(salesOrdersAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/salesOrders/${customerId}`
      const salesOrders = await _fetchData(url)
      setSalesOrders(salesOrders)
    }

    fetchData()
  }, [customerId, setSalesOrders])

  return salesOrders
}

// React hook for using dashboard data inn a component
function useInitializeDashboardData(customerId) {
  const [dashboardData, setDashboardData] = useAtom(dashboardAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/dashboard/${customerId}`
      const dashboardData = await _fetchData(url)
      console.log('Dashbaord Data from Server', dashboardData)
      setDashboardData(dashboardData)
    }

    fetchData()
  }, [customerId, setDashboardData])

  return dashboardData
}
// Fetch transactions by customerId
function useFetchTransactionsByCustomerId(customerId) {
  const [customerPayments, setCustomerPayments] = useAtom(customerPaymentsAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/customerPayments/${customerId}`
      const customerPayments = await _fetchData(url)
      setCustomerPayments(customerPayments)
    }

    fetchData()
  }, [customerId, setCustomerPayments])

  return customerPayments
}

// Fetch transactions by customerId
function useFetchCreditNotesByCustomerId(customerId) {
  const [creditNotes, setCreditNotes] = useAtom(customerPaymentsAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/creditNotes/${customerId}`
      const creditNotes = await _fetchData(url)
      setCreditNotes(creditNotes)
    }

    fetchData()
  }, [customerId, setCreditNotes])

  return creditNotes
}

function useFetchMasterDataByCustomerId(customerId) {
  const [salesMasterData, setSalesMasterData] = useAtom(salesMasterDataAtom) // Initialize atom

  useEffect(() => {
    // Define the asynchronous data fetching function
    const fetchData = async () => {
      if (!apiDomain || !customerId) return // Handle undefined values

      try {
        const url = `${apiDomain}/salesMasterData/${customerId}`
        const masterData = await _fetchData(url)
        setSalesMasterData(masterData) // Set the master data in the atom state
      } catch (error) {
        console.log('Error fetching master data:', error)
      }
    }

    fetchData() // Invoke the data fetch function

    // Optional: You can add cleanup logic here if necessary
  }, [customerId, setSalesMasterData]) // Trigger effect when customerId changes

  return salesMasterData // Return the current state of the master data
}

function useFetchDuesDataByCustomerId(customerId) {
  const [duesData, setDuesData] = useAtom(duesAtom) // Initialize atom

  useEffect(() => {
    // Define the asynchronous data fetching function
    const fetchData = async () => {
      if (!apiDomain || !customerId) return // Handle undefined values

      try {
        const url = `${apiDomain}/dues/${customerId}`
        const duesData = await _fetchData(url)
        setDuesData(duesData) // Set the master data in the atom state
      } catch (error) {
        console.log('Error fetching master data:', error)
      }
    }

    fetchData() // Invoke the data fetch function

    // Optional: You can add cleanup logic here if necessary
  }, [customerId, setDuesData]) // Trigger effect when customerId changes

  return duesData // Return the current state of the master data
}

// Fetch transactions by customerId
function useFetchAccountStatementByCustomerId(customerId) {
  const [customerStatement, setCustomerStatement] = useAtom(customerStatementAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/accountStatementFinal/${customerId}`
      const customerStatement = await _fetchData(url)
      setCustomerStatement(customerStatement?.results)
    }

    fetchData()
  }, [customerId, setCustomerStatement])

  return customerStatement
}

// Fetch farmers  Visits by retailer customerId
function useFetchFarmersVisitsByCustomerId(customerId) {
  const [visits, setVisits] = useAtom(farmerVisitsAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/farmerVisits/${customerId}`
      const visits = await _fetchData(url)
      setVisits(visits)
    }

    fetchData()
  }, [customerId, setVisits])

  return visits
}

// Fetch farmers  Visits by retailer customerId
function useFetchSMRReportByCustomerId(customerId) {
  const [smrReport, setSMRReport] = useAtom(smrReportAtom)
  const [modifiedSmrReport, setModifiedSmrReport] = useState([])

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/smrReport/${customerId}`
      const report = await _fetchData(url)
      setSMRReport(report.results)
      setModifiedSmrReport(report.results)
    }

    fetchData()
  }, [customerId, setSMRReport])

  return { smrReport, modifiedSmrReport, setModifiedSmrReport }
}

// Fetch Product Catalogue
function useFetchProductCatalogue(customerId) {
  const [productCatalogue, setProductCatalogue] = useAtom(productCatalogueAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/productCatalogue/${customerId}`
      const productCatalogue = await _fetchData(url)
      console.log('Product Catelog', productCatalogue)
      setProductCatalogue(productCatalogue)
    }

    fetchData()
  }, [customerId, setProductCatalogue])

  return productCatalogue
}

// Fetch Product Catalogue
function useFetchCarouselData(customerId) {
  const [carouselData, setCarouselData] = useAtom(carouselDataAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/carouselData/${customerId}`
      const carouselData = await _fetchData(url)
      setCarouselData(carouselData)
    }

    fetchData()
  }, [customerId, setCarouselData])

  return carouselData
}

// Fetch Product Catalogue
function useFetchScheme(customerId) {
  const [schemeData, setSchemeData] = useAtom(schemeDataAtom)

  useEffect(() => {
    const fetchData = async () => {
      if (!apiDomain || !customerId) return []
      const url = `${apiDomain}/scheme/${customerId}`
      const schemelData = await _fetchData(url)
      setSchemeData(schemelData)
    }

    fetchData()
  }, [customerId, setSchemeData])

  return schemeData
}

export {
  useFetchAccountStatementByCustomerId,
  useFetchCarouselData,
  useFetchCreditNotesByCustomerId,
  useFetchDuesDataByCustomerId,
  useFetchFarmersVisitsByCustomerId,
  useFetchInvoicesByCustomerId,
  useFetchMasterDataByCustomerId,
  useFetchPriceListData,
  useFetchProductCatalogue,
  useFetchSMRReportByCustomerId,
  useFetchSalesOrdersByCustomerId,
  useFetchScheme,
  useFetchSupportPersionsData,
  useFetchTransactionsByCustomerId,
  useInitializeDashboardData,
  validateRetailer,
}
