'use client'
import { ProductDetailView } from '@/app/(aquapartner)/products/components/productDetailsView'
import { Badge } from '@/components/badge'
import { DueCards } from '@/customComponents/DueCards'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { MyFarmerDetails } from '@/customComponents/MyFarmerDetails'
import { PaymentApexChart } from '@/customComponents/PaymentApexChart'
import { PriceCard } from '@/customComponents/PriceCard'
import { PurchaseApexChart } from '@/customComponents/PurchaseApexChart'
import { RadioButtonListGroup } from '@/customComponents/RadioButtonListGroup'
import { dashboardAtom, priceListAtom, retailerAtom } from '@/customComponents/providers'
import { priceList } from '@/utils/price-list'
import { useFetchCarouselData, useInitializeDashboardData } from '@/utils/requests'
import { Dialog, DialogPanel, DialogTitle } from '@headlessui/react'
import { useAtom, useAtomValue } from 'jotai'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import 'react-multi-carousel/lib/styles.css'

interface Product {
  productImage: string
  productName: string
  content: string
  tagLine: string
}

export default function Home() {
  const [priceListValue, setPriceListValue] = useAtom(priceListAtom)
  var customer = useAtomValue(retailerAtom)
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setDialogOpen] = useState(false)
  const [product, setProduct] = useState<Product | null>(null)
  const [showProductDetails, setShowProductDetails] = useState(false)
  useInitializeDashboardData(customer.customerId)
  const data = useFetchCarouselData(customer.customerId)
  /* const { results: schmeData } = useFetchScheme(customer.customerId)
  const { results: supportPersionsDetails } = useFetchSupportPersionsData(customer.customerId) **/
  const dashboardData = useAtomValue(dashboardAtom)

  const router = useRouter()
  useEffect(() => {
    if (data?.results && dashboardData) {
      setLoading((prev) => false)
    }
  }, [data, dashboardData])

  // Old No: 7981693028

  // New No:  9493222223

  //  Vedanta Aqua Future and Needs

  const producctClicked = (product: {
    productImage: string
    productName: string
    content: string
    tagLine: string
  }) => {
    setProduct((prev) => product)
    setShowProductDetails(true)
  }

  const [selectedValue, setSelectedValue] = useState('All') // Holds the selected radio button value

  const [buttonList] = useState(() => [
    { label: 'All', value: 'All' },
    { label: 'Current Month', value: 'Current Month' },
    { label: 'Last Month', value: 'Last Month' },
  ])

  useEffect(() => {
    if (priceListValue.state === '') {
      setPriceListValue(priceList[0])
    }
  }, [priceListValue, setPriceListValue])

  const handleChangeState = () => {
    setDialogOpen((prev) => !prev)
  }

  const handleStateChange = (state: any) => {
    setPriceListValue(state)
    setDialogOpen((prev) => !prev)
  }

  // schmeData.payment schmeData.drGrowSales schmeData.total
  return (
    <div className="">
      {loading && <LoadingScreen />}
      {/*schmeData && supportPersionsDetails && (
        <RewardSchemeQ3
          totalPayment={schmeData.payment}
          totalPurchase={schmeData.sales}
          supportPersion={supportPersionsDetails[0]?.SO}
        />
      ) */}
      <div
        onClick={() => {
          router.push('/products?page=Catalogue')
        }}
        className="h-64 cursor-pointer bg-dr-grow-banner-mobile bg-center bg-no-repeat md:bg-dr-grow-banner-desktop"
      />
      {priceListValue.state !== '' && (
        <div className="flex flex-col justify-start pb-6 pt-6">
          <h2 className="pb-2 font-inter text-lg font-semibold capitalize text-[#111827]">Price List</h2>
          <div className="flex flex-row justify-between">
            <div className="flex flex-row">
              <img
                src="/images/location-icon.svg" // Replace with your icon URL
                alt="location Icon"
                className="mr-2 h-6 w-6"
              />
              <p className="">{priceListValue.state}</p>
            </div>
            <button className="flex text-base text-blue-600 hover:underline" onClick={handleChangeState}>
              Change
            </button>
          </div>
          <PriceCard
            title={'Vennamei'}
            date={priceListValue.date}
            isSharable={false}
            priceData={priceListValue.prices}
          />
        </div>
      )}
      <MyFarmerDetails />
      <div className="flex flex-col justify-start space-x-6 sm:flex-row">
        <h2 className="pb-2 font-inter text-lg font-semibold capitalize text-[#111827]">Overview</h2>
        <RadioButtonListGroup
          buttonList={buttonList}
          selectedValue={selectedValue}
          setSelectedValue={setSelectedValue}
        />
      </div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div className="rounded-lg border border-[#E5E7EB] bg-[#F9FAFB] px-4 py-2">
          <PurchaseApexChart selectedValue={selectedValue} />
        </div>
        <div className="rounded-lg border border-[#E5E7EB] bg-[#F9FAFB] px-4 py-2">
          <PaymentApexChart selectedValue={selectedValue} />
        </div>
      </div>
      <div className="mt-6">
        <div className="w-full">
          <DueCards />
        </div>
      </div>
      {data?.results && (
        <div className="mt-6">
          <h2 className="font-inter text-lg font-semibold capitalize text-[#111827]">Recent Products</h2>
          <div className="container h-auto">
            <ul className="-mx-6 flex flex-wrap px-4">
              {data?.results.map(
                (
                  item: {
                    productImage: string
                    productName: string
                    content: string
                    tagLine: string
                    productTag: string
                    sortOrder: string
                  },
                  index: number
                ) => (
                  <li key={index} className="w-full cursor-pointer p-1 md:w-1/3" onClick={() => producctClicked(item)}>
                    <div className="relative mx-auto my-0 flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50">
                      <div className="flex gap-2">
                        <div className="w-30 shrink-0 p-2">
                          <img
                            className="h-24 w-24 rounded-lg border border-gray-100 bg-[#E5E7EB] p-2"
                            src={item.productImage}
                            alt=""
                          />
                        </div>
                        <div className="flex flex-col justify-center">
                          <div className="heading">
                            <h2 className="text-sm font-semibold uppercase text-gray-400">{'Dr.Grow'}</h2>
                          </div>
                          <div className="flex items-center">
                            <h1 className="flex items-center gap-2 text-lg font-semibold uppercase text-gray-700">
                              {item.productName}
                            </h1>
                            <Badge
                              className="absolute right-0 top-0 rounded-none rounded-tr-lg p-4 text-xs uppercase"
                              color={'yellow'}
                            >
                              {item.productTag}
                            </Badge>
                          </div>
                          <div className="mb-2 text-sm text-gray-500 sm:text-base">{''}</div>
                        </div>
                      </div>
                    </div>
                  </li>
                )
              )}
            </ul>
          </div>
        </div>
      )}
      <div className="mt-6 grow flex-col">
        <div className="mb-0 font-inter text-[14px] text-sm font-normal capitalize leading-[24px] text-[#4B5563]">
          <p className="pb-1 font-inter text-lg font-semibold uppercase leading-tight text-[#111827]">
            <span className="font-bold">GOTO</span>
          </p>
        </div>
        <div className="flex justify-between">
          <a href="/priceList">
            <div className="flex grow basis-0 flex-col items-center justify-start gap-2">
              <div className="inline-flex items-center justify-center gap-2.5 rounded border bg-[#f9fafc] p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#1544ce" className="size-8">
                  <path d="M12 7.5a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5Z" />
                  <path
                    fillRule="evenodd"
                    d="M1.5 4.875C1.5 3.839 2.34 3 3.375 3h17.25c1.035 0 1.875.84 1.875 1.875v9.75c0 1.036-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 0 1 1.5 14.625v-9.75ZM8.25 9.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM18.75 9a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V9.75a.75.75 0 0 0-.75-.75h-.008ZM4.5 9.75A.75.75 0 0 1 5.25 9h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H5.25a.75.75 0 0 1-.75-.75V9.75Z"
                    clipRule="evenodd"
                  />
                  <path d="M2.25 18a.75.75 0 0 0 0 1.5c5.4 0 10.63.722 15.6 2.075 1.19.324 2.4-.558 2.4-1.82V18.75a.75.75 0 0 0-.75-.75H2.25Z" />{' '}
                </svg>
              </div>
              <div className="self-stretch text-center font-['Inter'] text-xs font-medium leading-[14px] text-[#0f1525]">
                {/*   <div className="hidden sm:block">Pricelist</div> */}
                Pricelist
              </div>
            </div>
          </a>

          <a href="/accountStatement">
            <div className="flex grow basis-0 flex-col items-center justify-start gap-2">
              <div className="inline-flex items-center justify-center gap-2.5 rounded border bg-[#f9fafc] p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#1544ce" className="size-8">
                  <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z" />
                  <path d="M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z" />
                </svg>
              </div>
              <div className="self-stretch text-center font-['Inter'] text-xs font-medium leading-[14px] text-[#0f1525]">
                <div className="hidden sm:block">Account</div>
                Statement
              </div>
            </div>
          </a>
          <a href="/billingAndPayments?page=Sales%20Orders">
            <div className="flex grow basis-0 flex-col items-center justify-start gap-2">
              <div className="inline-flex items-center justify-center gap-2.5 rounded border bg-[#f9fafc] p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#1544ce" className="size-8">
                  <path
                    fillRule="evenodd"
                    d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM9 7.5A.75.75 0 0 0 9 9h1.5c.98 0 1.813.626 2.122 1.5H9A.75.75 0 0 0 9 12h3.622a2.251 2.251 0 0 1-2.122 1.5H9a.75.75 0 0 0-.53 1.28l3 3a.75.75 0 1 0 1.06-1.06L10.8 14.988A3.752 3.752 0 0 0 14.175 12H15a.75.75 0 0 0 0-1.5h-.825A3.733 3.733 0 0 0 13.5 9H15a.75.75 0 0 0 0-1.5H9Z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="self-stretch text-center font-['Inter'] text-xs font-medium leading-[14px] text-[#0f1525]">
                Billing
              </div>
            </div>
          </a>
          <a href="/myFarmers">
            <div className="inline-flex shrink grow basis-0 flex-col items-center justify-start gap-2">
              <div className="inline-flex items-center justify-center gap-2.5 rounded border bg-[#f9fafc] p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#1544ce" className="size-8">
                  <path
                    fillRule="evenodd"
                    d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-2.625 6c-.54 0-.828.419-.936.634a1.96 1.96 0 0 0-.189.866c0 .298.059.605.189.866.108.215.395.634.936.634.54 0 .828-.419.936-.634.13-.26.189-.568.189-.866 0-.298-.059-.605-.189-.866-.108-.215-.395-.634-.936-.634Zm4.314.634c.108-.215.395-.634.936-.634.54 0 .828.419.936.634.13.26.189.568.189.866 0 .298-.059.605-.189.866-.108.215-.395.634-.936.634-.54 0-.828-.419-.936-.634a1.96 1.96 0 0 1-.189-.866c0-.298.059-.605.189-.866Zm2.023 6.828a.75.75 0 1 0-1.06-1.06 3.75 3.75 0 0 1-5.304 0 .75.75 0 0 0-1.06 1.06 5.25 5.25 0 0 0 7.424 0Z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="self-stretch text-center font-['Inter'] text-xs font-medium leading-[14px] text-[#0f1525]">
                Farmers
              </div>
            </div>
          </a>
          <a href="/products?page=Catalogue">
            <div className="inline-flex shrink grow basis-0 flex-col items-center justify-start gap-2">
              <div className="inline-flex items-center justify-center gap-2.5 rounded border bg-[#f9fafc] p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#1544ce" className="size-8">
                  <path d="M12.378 1.602a.75.75 0 0 0-.756 0L3 6.632l9 5.25 9-5.25-8.622-5.03ZM21.75 7.93l-9 5.25v9l8.628-5.032a.75.75 0 0 0 .372-.648V7.93ZM11.25 22.18v-9l-9-5.25v8.57a.75.75 0 0 0 .372.648l8.628 5.033Z" />
                </svg>
              </div>
              <div className="self-stretch text-center font-['Inter'] text-xs font-medium leading-[14px] text-[#0f1525]">
                Products
              </div>
            </div>
          </a>
          <a href="/inventory">
            <div className="inline-flex shrink grow basis-0 flex-col items-center justify-start gap-2">
              <div className="inline-flex items-center justify-center rounded border bg-[#f9fafc] p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#1544ce" className="size-8">
                  <path d="M15 3.75H9v16.5h6V3.75ZM16.5 20.25h3.375c1.035 0 1.875-.84 1.875-1.875V5.625c0-1.036-.84-1.875-1.875-1.875H16.5v16.5ZM4.125 3.75H7.5v16.5H4.125a1.875 1.875 0 0 1-1.875-1.875V5.625c0-1.036.84-1.875 1.875-1.875Z" />
                </svg>
              </div>
              <div className="self-stretch text-center font-['Inter'] text-xs font-medium leading-[14px] text-[#0f1525]">
                Stocks
              </div>
            </div>
          </a>
        </div>
      </div>
      {showProductDetails && (
        <ProductDetailView open={showProductDetails} setOpen={setShowProductDetails} product={product} />
      )}
      {isDialogOpen && (
        <Dialog open={isDialogOpen} onClose={() => setDialogOpen(false)} className="relative z-50">
          <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

          <div className="fixed inset-0 flex items-center justify-center p-4">
            <DialogPanel className="mx-auto max-w-sm rounded bg-white p-6">
              <DialogTitle className="mb-4 text-lg font-medium">Select State</DialogTitle>
              <div className="space-y-2">
                {priceList.map((state) => (
                  <label key={state.state} className="flex cursor-pointer items-center space-x-2">
                    <input
                      type="radio"
                      name="state"
                      value={state.state}
                      checked={priceListValue.state === state.state}
                      onChange={() => handleStateChange(state)}
                      className="form-radio text-blue-600"
                    />
                    <span>{state.state}</span>
                  </label>
                ))}
              </div>
            </DialogPanel>
          </div>
        </Dialog>
      )}
    </div>
  )
}
