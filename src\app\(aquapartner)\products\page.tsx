'use client'

import CategoryFilter from '@/customComponents/CategoryFilter'
import LoadingScreen from '@/customComponents/LoadingScreen'
import ProductCard from '@/customComponents/ProductItemCard'
import TabComponent from '@/customComponents/TabComponent'
import { retailerAtom, selectedTabAtom } from '@/customComponents/providers'
import { useFetchProductCatalogue } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import { useEffect, useRef, useState } from 'react'
import { ProductDetailView } from './components/productDetailsView'
const Products = () => {
  const [product, setProduct] = useState(null)
  const [selectedProducts, setSelectedProducts] = useState([])

  // Added a selectedCategory state to track the currently chosen subcategory in the parent
  const [selectedCategory, setSelectedCategory] = useState('All')

  const [loading, setLoading] = useState(true)
  const [showProductDetails, setShowProductDetails] = useState(false)
  const customer = useAtomValue(retailerAtom)
  const productCatalogue = useFetchProductCatalogue(customer.customerId)
  const activeTab = useAtomValue(selectedTabAtom)
  const [subCategories, setSubCategories] = useState<string[]>([])
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const [tabsData, setTabsData] = useState([
    { label: 'Dr.Grow', count: 7 },
    { label: 'Others', count: 31 },
  ])

  useEffect(() => {
    if (productCatalogue?.results) {
      setLoading(false)

      // Filter based on activeTab
      let filtered = []
      if (activeTab === 0) {
        filtered = productCatalogue.results.filter((brand: any) => brand.sortOrder === 1)
        tabsData[0].count = filtered.reduce((total: number, category: any) => {
          // Check if the category has a products array before adding its length
          return total + (Array.isArray(category.products) ? category.products.length : 0)
        }, 0)
      } else {
        filtered = productCatalogue.results.filter((brand: any) => brand.sortOrder !== 1)
        tabsData[1].count = filtered.reduce((total: number, category: any) => {
          // Check if the category has a products array before adding its length
          return total + (Array.isArray(category.products) ? category.products.length : 0)
        }, 0)
      }

      setSelectedProducts(filtered)

      // Gather subcategories for the CategoryFilter
      const allSubs = filtered.flatMap((brand: any) => brand.products.map((p: any) => p.subCategory))
      const uniqueSubs: string[] = Array.from(new Set(allSubs))
      setSubCategories(uniqueSubs)
    }
  }, [productCatalogue, activeTab, tabsData])

  // Reset the category filter to "All" each time the active tab changes
  useEffect(() => {
    setSelectedCategory('All')
  }, [activeTab])

  // Add this useEffect to handle scroll containment
  useEffect(() => {
    if (selectedCategory === 'All') {
    }
  }, [selectedCategory, subCategories]) // Trigger when selection or categories change

  const productClicked = (clickedProduct: any) => {
    setProduct(clickedProduct)
    setShowProductDetails(true)
  }

  // Adjust the selectedProducts based on the chosen subCategory
  const handleCategoryChange = (subCategory: any) => {
    setSelectedCategory(subCategory)

    if (!productCatalogue?.results) return

    if (subCategory === 'All') {
      // Keep the existing logic for filtering by activeTab
      if (activeTab === 0) {
        setSelectedProducts(productCatalogue.results.filter((brand: any) => brand.sortOrder === 1))
      } else {
        setSelectedProducts(productCatalogue.results.filter((brand: any) => brand.sortOrder !== 1))
      }
      return
    }

    // Filter results by chosen subCategory
    const filtered = productCatalogue.results
      .filter((brand: any) => (activeTab === 0 ? brand.sortOrder === 1 : brand.sortOrder !== 1))
      .map((brand: any) => ({
        ...brand,
        products: brand.products.filter((p: any) => p.subCategory === subCategory),
      }))
      .filter((brand: any) => brand.products.length > 0)

    setSelectedProducts(filtered)
  }

  return (
    <>
      {loading && <LoadingScreen />}
      <div className="mt-2 pb-6 align-baseline sm:flex sm:justify-between">
        <div className="flex flex-col sm:flex-row">
          <p className="text-2xl font-bold">Products</p>
        </div>
      </div>

      <TabComponent tabs={tabsData} />

      {/* Pass selectedCategory and subCategories to CategoryFilter */}
      <CategoryFilter
        onFilterChange={handleCategoryChange}
        subCategories={['All', ...subCategories]}
        selectedCategory={selectedCategory} // Provide the current selected subcategory
        //
        //   containerRef={scrollContainerRef}
      />

      <div className="flex min-h-screen flex-row overflow-y-auto scroll-smooth">
        <div className="flex w-screen flex-col">
          <ul className="mt-0 flex-1">
            {selectedProducts.map((brand: any, brandIndex) =>
              brand.products.map((prod: any, productIndex: any) => (
                <div key={productIndex} className="w-full cursor-pointer" onClick={() => productClicked(prod)}>
                  <ProductCard
                    title={brand.name}
                    subtitle={prod.productName}
                    description={prod.tagLine}
                    imageUrl={prod.productImage} /* example path; adapt as needed */
                    productTag={prod.productTag}
                  />
                </div>
              ))
            )}
          </ul>
          {showProductDetails && (
            <ProductDetailView open={showProductDetails} setOpen={setShowProductDetails} product={product} />
          )}
        </div>
      </div>
    </>
  )
}

export default Products
