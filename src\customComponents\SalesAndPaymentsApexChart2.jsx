'use client'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useCallback, useEffect, useState } from 'react'
import { getWeeksInMonth } from '../utils/date'
import { getMonthsArray } from '../utils/extras'
import Apex<PERSON>hart from './ApexChart'
import { dashboardAtom } from './providers'

export const SalesAndPaymentsApexChart2 = ({ selectedValue }) => {
  const data = useAtomValue(dashboardAtom)

  const [labels, setLabels] = useState(() => getMonthsArray())
  const [currentYear] = useState(() => moment().year())
  const [currentMonthIndex] = useState(() => moment().month())

  // Initialize arrays with dynamic size based on the current month
  const [salesValues, setSalesValues] = useState(() => Array(currentMonthIndex + 1).fill(0))

  const populateMonthValues = useCallback(
    (sourceData, setTargetArray, type) => {
      if (sourceData) {
        const updatedArray = [...Array(currentMonthIndex + 1).fill(0)] // Create a new array for updates
        sourceData.forEach((obj) => {
          const key = Object.keys(obj)[0]
          updatedArray[parseInt(key, 10) - 1] = obj[key]['totalSales'] // Adjust for 1-based month index
        })
        setTargetArray(updatedArray) // Update the state with the new array
      }
    },
    [currentMonthIndex]
  )
  const populateWeekValues = useCallback((sourceData, setTargetArray, weeksLength, selectedMonth) => {
    if (sourceData) {
      const updatedArray = [...Array(weeksLength).fill(0)] // Create a new array for updates
      const selectedMonthData = sourceData.find((obj) => obj[selectedMonth])
      if (selectedMonthData)
        selectedMonthData[Object.keys(selectedMonthData)[0]].weeks.forEach((obj) => {
          updatedArray[Number(Object.keys(obj)[0]) - 1] = obj[Object.keys(obj)[0]]
        })
      setTargetArray(updatedArray) // Update the state with the new array
    }
  }, [])

  useEffect(() => {
    if (data.sales && data.payments) {
      const salesData = data.sales[currentYear]
      populateMonthValues(salesData, setSalesValues, 'Sales')
    }
  }, [data, currentYear, currentMonthIndex, populateMonthValues]) // Re-run the effect if data or the current time changes

  useEffect(() => {
    if (!data?.sales) return
    const salesData = data.sales[currentYear]

    if (selectedValue === 'All') {
      setLabels(getMonthsArray())
      populateMonthValues(salesData, setSalesValues, 'Sales')
      return
    }
    const weeks = getWeeksInMonth(selectedValue, currentYear)
    setLabels(weeks)
    populateWeekValues(salesData, setSalesValues, weeks.length, selectedValue)
  }, [data.sales, selectedValue, currentYear, populateMonthValues, populateWeekValues])

  const series = [
    {
      name: 'Purchases',
      data: salesValues,
      color: '#07D6F8',
    },
  ]

  const options = {
    chart: {
      type: 'line',
      fontFamily: 'Inter, sans-serif',
      dropShadow: {
        enabled: false,
      },
      toolbar: {
        show: false, // Hide the toolbar for a clean look
      },
    },
    title: {
      text: 'Purchases', // Chart title
      align: 'left',
      offsetY: -5,
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#333', // Customize title color
        fontFamily: 'Inter, sans-serif',
      },
    },
    tooltip: {
      enabled: true,
      x: {
        show: false,
      },
    },
    dataLabels: {
      enabled: true, // Enable data labels
      style: {
        colors: ['#000'], // Set label color to black
        fontSize: '12px',
        fontWeight: 'bold',
      },
      offsetY: -20,
      formatter: function (value, series) {
        return value >= 1000 ? (value / 1000).toFixed(1) : value ? value : ''
      },
    },
    stroke: {
      width: 0,
      curve: 'smooth',
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          plotOptions: {
            bar: {
              dataLabels: {
                orientation: 'vertical',
                position: 'bottom',
              },
            },
          },
          title: {
            style: {
              fontSize: '16px',
            },
          },
        },
      },
    ],
    plotOptions: {
      bar: {
        dataLabels: {
          position: 'top',
          // offsetY: 10,
        },
      },
    },
    grid: {
      show: true,
      strokeDashArray: 4,
      padding: {
        right: 2,
        top: -26,
      },
    },
    xaxis: {
      categories: labels,
      tickAmount: labels.length,
      labels: {
        show: true,
        style: {
          fontFamily: 'Inter, sans-serif',
          cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400',
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
    },
    legend: {
      show: true, // Enable the legend
      showForSingleSeries: true,
      position: 'bottom', // Position the legend at the bottom
      horizontalAlign: 'center', // Align it to the center
      labels: {
        colors: '#333', // Customize legend text color
        useSeriesColors: false,
      },
      markers: {
        width: 10,
        height: 10,
        radius: 12,
      },
      fontFamily: 'Inter, sans-serif',
      formatter: function (value, series) {
        return value + ' (in Thousands)'
      },
    },
  }
  return <ApexChart options={options} series={series} type="bar" />
}

export const SalesAndPaymentsApexChartMixed2 = ({ selectedValue }) => {
  const data = useAtomValue(dashboardAtom)
  const [labels, setLabels] = useState(() => getMonthsArray())
  const [currentYear] = useState(() => moment().year())
  const [currentMonthIndex] = useState(() => moment().month())

  // Initialize arrays with dynamic size based on the current month
  const [paymentValues, setPaymentValues] = useState(() => Array(currentMonthIndex + 1).fill(0))

  const populateMonthValues = useCallback(
    (sourceData, setTargetArray, type) => {
      if (sourceData) {
        const updatedArray = Array(currentMonthIndex + 1).fill(0) // Create a new array for updates
        sourceData.forEach((obj) => {
          const key = Object.keys(obj)[0]
          updatedArray[parseInt(key, 10) - 1] = obj[key]['totalPayment'] // Adjust for 1-based month index
        })
        setTargetArray(updatedArray) // Update the state with the new array
      }
    },
    [currentMonthIndex]
  )
  const populateWeekValues = useCallback((sourceData, setTargetArray, weeksLength, selectedMonth) => {
    if (sourceData) {
      const updatedArray = Array(weeksLength).fill(0) // Create a new array for updates
      const selectedMonthData = sourceData.find((obj) => obj[selectedMonth])
      if (selectedMonthData)
        selectedMonthData[Object.keys(selectedMonthData)[0]].weeks.forEach((obj) => {
          updatedArray[Number(Object.keys(obj)[0]) - 1] = obj[Object.keys(obj)[0]]
        })
      setTargetArray(updatedArray) // Update the state with the new array
    }
  }, [])

  useEffect(() => {
    if (data.sales && data.payments) {
      const paymentsData = data.payments[currentYear]
      populateMonthValues(paymentsData, setPaymentValues, 'Payments')
    }
  }, [data, currentYear, currentMonthIndex, populateMonthValues]) // Re-run the effect if data or the current time changes

  useEffect(() => {
    if (!data?.payments) return
    const paymentsData = data.payments[currentYear]

    if (selectedValue === 'All') {
      setLabels(getMonthsArray())
      populateMonthValues(paymentsData, setPaymentValues, 'Payments')
      return
    }
    const weeks = getWeeksInMonth(selectedValue, currentYear)
    setLabels(weeks)
    populateWeekValues(paymentsData, setPaymentValues, weeks.length, selectedValue)
  }, [data.payments, selectedValue, currentYear, populateMonthValues, populateWeekValues])

  const series = [
    {
      name: 'Payments2',
      type: 'bar',
      data: paymentValues,
      color: '#42D1B5',
    },
  ]

  const options = {
    chart: {
      type: 'line', // Set chart to line type, but series can have individual types
      fontFamily: 'Inter, sans-serif',
      dropShadow: {
        enabled: false,
      },
      toolbar: {
        show: false,
      },
    },
    title: {
      text: 'Payments2', // Chart title
      align: 'left',
      offsetY: -5,
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#333', // Customize title color
        fontFamily: 'Inter, sans-serif',
      },
    },
    tooltip: {
      enabled: true,
      x: {
        show: false,
      },
    },
    dataLabels: {
      enabled: true, // Enable data labels
      style: {
        colors: ['#000'], // Set label color to black
        fontSize: '12px',
        fontWeight: 'bold',
      },
      offsetY: -20,
      formatter: function (value, series) {
        return value >= 1000 ? (value / 1000).toFixed(1) : value ? value : ''
      },
    },
    stroke: {
      width: [0, 6], // Width of the line, 0 for bars, 6 for line
      curve: 'smooth', // Apply smooth curve to line chart
    },
    grid: {
      show: true,
      strokeDashArray: 4,
      padding: {
        left: 2,
        right: 2,
        top: -26,
      },
    },
    plotOptions: {
      bar: { horizontal: true }, // Convert to horizontal bar chart
      bar: {
        // columnWidth: '50%', // Adjust the width of the bars
        // endingShape: 'rounded', // Option for rounded bars
        dataLabels: {
          position: 'top',
          // offsetY: 10,
        },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          plotOptions: {
            bar: {
              dataLabels: {
                orientation: 'vertical',
                position: 'bottom',
              },
            },
          },
          title: {
            style: {
              fontSize: '16px',
            },
          },
        },
      },
    ],
    xaxis: {
      // assuming `labels` is defined
      //  tickAmount: labels.length, // Ensure all labels are shown
      labels: {
        show: true,
        style: {
          fontFamily: 'Inter, sans-serif',
          cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400',
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      categories: labels,
      labels: {
        show: true, // Hide the y-axis labels
      },
      axisBorder: {
        show: false, // Hide the y-axis border
      },
      axisTicks: {
        show: false, // Hide the y-axis ticks
      },
      grid: {
        show: false, // Hide gridlines along the y-axis
      },
    },
    legend: {
      show: true,
      showForSingleSeries: true,
      position: 'bottom',
      horizontalAlign: 'center',
      labels: {
        colors: '#333',
        useSeriesColors: false,
      },
      markers: {
        width: 10,
        height: 10,
        radius: 12,
      },
      fontFamily: 'Inter, sans-serif',
      formatter: function (value, series) {
        return value + ' (in Thousands)'
      },
    },
  }

  return <ApexChart options={options} series={series} type="bar" />
}
