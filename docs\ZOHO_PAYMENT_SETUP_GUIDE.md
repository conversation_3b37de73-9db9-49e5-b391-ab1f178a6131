# Zoho Payment API Setup Guide

## Overview

This guide walks you through the complete setup process for integrating Zoho Payment Gateway with the AquaPartner application. Follow these steps to enable payment processing functionality.

## Prerequisites

- AquaPartner application running locally or on server
- Zoho Developer Console access
- MongoDB database connection
- Valid Zoho Pay account

## Step 1: Zoho Developer Console Setup

### 1.1 Create Zoho Application

1. Go to [Zoho Developer Console](https://api-console.zoho.in/)
2. Click "Add Client" → "Server-based Applications"
3. Fill in the application details:
   - **Client Name**: AquaPartner Payment Integration
   - **Homepage URL**: https://your-domain.com
   - **Authorized Redirect URIs**: https://your-domain.com/auth/callback
4. Note down the **Client ID** and **Client Secret**

### 1.2 Configure Scopes

Add the following scopes for Zoho Pay:
- `ZohoPay.payments.CREATE`
- `ZohoPay.payments.READ`
- `ZohoPay.refunds.CREATE`
- `ZohoPay.refunds.READ`

## Step 2: Environment Variables Configuration

Update your `.env.local` file with the Zoho credentials:

```env
# Zoho OAuth Configuration
ZOHO_OAUTH_CLIENT_ID=your_client_id_here
ZOHO_OAUTH_CLIENT_SECRET=your_client_secret_here
ZOHO_OAUTH_REDIRECT_URI=https://your-domain.com/auth/callback

# Zoho Pay Configuration
ZOHO_PAY_ACCOUNT_ID=your_account_id_here
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.in/api/v1/paymentsessions

# Database
MONGODB_URI=your_mongodb_connection_string
```

## Step 3: Generate Authorization Code

### 3.1 Authorization URL

Create the authorization URL using your client ID:

```
https://accounts.zoho.in/oauth/v2/auth?scope=ZohoPay.payments.CREATE,ZohoPay.payments.READ,ZohoPay.refunds.CREATE,ZohoPay.refunds.READ&client_id=YOUR_CLIENT_ID&response_type=code&redirect_uri=YOUR_REDIRECT_URI&access_type=offline
```

### 3.2 Get Authorization Code

1. Open the authorization URL in your browser
2. Log in to your Zoho account
3. Grant permissions to the application
4. Copy the authorization code from the redirect URL

## Step 4: Initialize Zoho Tokens

### 4.1 Using API Endpoint

Make a POST request to initialize the tokens:

```bash
curl -X POST http://localhost:3000/api/zoho/auth/setup \
  -H "Content-Type: application/json" \
  -d '{
    "authorization_code": "YOUR_AUTHORIZATION_CODE_HERE"
  }'
```

### 4.2 Expected Response

```json
{
  "success": true,
  "message": "Zoho Payment tokens setup successfully",
  "data": {
    "token_id": "token_id_here",
    "expires_at": "2025-06-12T14:52:01.711Z",
    "scope": "ZohoPay.payments.CREATE,ZohoPay.payments.READ,ZohoPay.refunds.CREATE,ZohoPay.refunds.READ"
  }
}
```

## Step 5: Verify Setup

### 5.1 Health Check

Test the integration health:

```bash
curl http://localhost:3000/api/zoho/health
```

Expected response should show all checks as "healthy":

```json
{
  "status": "healthy",
  "checks": {
    "database": { "status": "healthy" },
    "environment": { "status": "healthy" },
    "zoho_auth": { "status": "healthy" },
    "zoho_api": { "status": "healthy" }
  }
}
```

### 5.2 Test Payment Session Creation

```bash
curl -X POST http://localhost:3000/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100.00,
    "currency": "INR",
    "description": "Test payment",
    "invoice_number": "TEST-001",
    "customer_id": "CUST-001",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "+919876543210"
  }'
```

## Step 6: Run Automated Tests

### 6.1 Simple Test Suite

```bash
node tests/simple-payment-test.js
```

### 6.2 Complete Test Suite

```bash
node tests/run-payment-tests.js
```

### 6.3 Browser Testing

Open `tests/payment-api-test.html` in your browser for interactive testing.

## Troubleshooting

### Common Issues

#### 1. "No Zoho Payment token found"

**Solution**: Complete Step 4 to initialize tokens

#### 2. "Invalid authorization code"

**Solutions**:
- Ensure the authorization code is recent (expires quickly)
- Verify the redirect URI matches exactly
- Check client ID and secret are correct

#### 3. "Token expired"

**Solution**: The system should auto-refresh tokens. If not working:
- Check the refresh token in the database
- Re-run the token setup process

#### 4. "Zoho API connectivity failed"

**Solutions**:
- Verify internet connectivity
- Check Zoho service status
- Ensure account ID is correct

### Database Verification

Check if tokens are stored correctly:

```javascript
// MongoDB query
db.ZohoPaymentTokens.find({ is_active: true }).sort({ created_at: -1 }).limit(1)
```

Expected fields:
- `access_token`
- `refresh_token`
- `expires_at`
- `is_active: true`

## Security Considerations

### Token Storage

- Tokens are encrypted in the database
- Access tokens expire and are auto-refreshed
- Only active tokens are used for API calls

### API Security

- All payment APIs require valid authentication
- Sensitive data is not logged
- HTTPS is required for production

### Environment Variables

- Never commit `.env.local` to version control
- Use different credentials for development/production
- Regularly rotate client secrets

## Production Deployment

### 1. Environment Setup

- Update environment variables for production
- Use production Zoho Pay account
- Configure proper redirect URIs

### 2. SSL Certificate

- Ensure HTTPS is enabled
- Zoho requires SSL for production

### 3. Monitoring

- Set up health check monitoring
- Monitor token refresh failures
- Track payment API response times

## API Endpoints Reference

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/zoho/health` | GET | Check integration health |
| `/api/zoho/auth/setup` | POST | Initialize OAuth tokens |
| `/api/zoho/payments/create-session` | POST | Create payment session |
| `/api/zoho/payments/status/{id}` | GET | Get payment status |
| `/api/initiatePayment` | POST | Legacy payment API |

## Support

For additional support:

1. Check the test report: `tests/ZOHO_PAYMENT_TEST_REPORT.md`
2. Review Zoho Pay documentation
3. Contact the development team

## Changelog

- **v1.0.0**: Initial setup guide
- **v1.1.0**: Added troubleshooting section
- **v1.2.0**: Added security considerations
