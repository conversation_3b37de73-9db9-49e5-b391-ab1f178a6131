import { Schema, model, models } from 'mongoose'

const CustomerSchema = new Schema(
  {
    customerId: String,
    customerName: String,
    Email: String,
    mobileNumber: String,
    customerCode: String,
    companyName: String,
    gstNo: String,
    address: String,
    businessVertical: String,
  },
  { collection: 'Customers' }
)

const Customer = models.Customers || model('Customers', CustomerSchema)

export default Customer
