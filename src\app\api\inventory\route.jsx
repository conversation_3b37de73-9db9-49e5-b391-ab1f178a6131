//import connectedDB from '@/config/database';
import connectedDB from '@/app/config/database'
import SMTracker from '@/app/models/SMTracker'

// GET /api/inventory/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const sMTracker = await SMTracker.find({
      $and: [
        {
          aquapartnerId: '401088000003303923',
        },

        {
          createdDateTime: {
            $gte: '2024-07-17T00:00:00Z',
            $lt: '2024-08-02T00:00:00Z',
          },
        },
      ],
    })

    // console.log(sMTracker);
    if (!sMTracker) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(sMTracker), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
