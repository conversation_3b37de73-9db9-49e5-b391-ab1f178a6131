import { Schema, model, models } from 'mongoose'

const SchemeNewSchema = new Schema(
  {
    customerId: String,
    aoName: String,
    asmName: String,
    customerName: String,
    drGrow: Number,
    drGrowPercent: Number,
    otherHC: Number,
    schemeWon: String,
    total: Number,
  },
  { collection: 'Scheme' }
)

const SchemeNew = models.Scheme || model('Scheme', SchemeNewSchema)

export default SchemeNew
