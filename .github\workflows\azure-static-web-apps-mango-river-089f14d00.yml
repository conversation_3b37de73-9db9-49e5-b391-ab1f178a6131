name: Azure Static Web Apps CI/CD

on:
  push:
    branches:
      - staging
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - staging

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
          lfs: false
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_MANGO_RIVER_089F14D00 }}
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          action: 'upload'
          app_location: '/'
          api_location: ''
          output_location: ''
        env:
          # Zoho Payment Configuration
          ZOHO_OAUTH_CLIENT_ID: ${{ secrets.ZOHO_OAUTH_CLIENT_ID }}
          ZOHO_OAUTH_CLIENT_SECRET: ${{ secrets.ZOHO_OAUTH_CLIENT_SECRET }}
          ZOHO_PAY_ACCOUNT_ID: ${{ secrets.ZOHO_PAY_ACCOUNT_ID }}
          ZOHO_WEBHOOK_SECRET: ${{ secrets.ZOHO_WEBHOOK_SECRET }}
          ZOHO_PAYMENT_SESSION_URL: https://payments.zoho.in/api/v1/paymentsessions

          # Database Configuration
          MONGODB_URI: ${{ secrets.MONGODB_URI }}

          # Environment Configuration
          NEXT_PUBLIC_DOMAIN: https://mango-river-089f14d00.3.azurestaticapps.net
          NODE_ENV: staging

  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
      - name: Close Pull Request
        id: closepullrequest
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_MANGO_RIVER_089F14D00 }}
          action: 'close'
