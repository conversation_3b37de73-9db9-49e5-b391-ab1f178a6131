'use clilent'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BarElement,
  CategoryScale,
  Chart as ChartJ<PERSON>,
  Legend,
  LineController,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
  elements,
  plugins,
} from 'chart.js'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { Chart } from 'react-chartjs-2'
import { getMonthsArray } from '../utils/extras'
import { dashboardAtom } from './providers'
ChartJS.register(
  CategoryScale,
  LinearScale,
  LineController,
  BarController,
  LineElement,
  PointElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  plugins,
  elements
)

const generateChartOption = ({ type, title = '' } = {}) => {
  const option = {
    type: type || 'bar',
    responsive: true,
    borderColor: 'rgba(127, 86, 217, 1)',
    borderDash: [5, 5],
    aspectRatio: 1.6,
    elements: {
      point: {
        pointStyle: false,
      },
    },
    interaction: {
      mode: 'index',
    },
    plugins: {
      datalabels: {
        formatter: (value, item) => {
          if (value > 0) {
            return (value / 1000).toFixed(1) + ' K'
          } else {
            return ''
          }
        },
        anchor: 'end', // Ensures labels are outside
        align: 'end', // Aligns the labels outside the chart
      },

      legend: {
        display: true,
        onClick: (e, legendItem, legend) => {},
        labels: {
          usePointStyle: true,

          generateLabels: (chart) => {
            let pointStyle = []
            chart.data.datasets.forEach((dataset) => {
              if (dataset.type === 'line') {
                pointStyle.push('line')
              } else {
                pointStyle.push('rect')
              }
            })
            return chart.data.datasets.map((dataset, index) => ({
              text: dataset.label,
              fillStyle: pointStyle[index] === 'rect' ? 'rgba(127, 86, 217, 1)' : dataset.backgroundColor,
              stokeStyle: dataset.borderColor,
              pointStyle: pointStyle[index],
              lineDash: pointStyle[index] === 'rect' ? [] : [5, 5],
              lineWidth: pointStyle[index] === 'rect' ? 0 : 3,
              hidden: false,
            }))
          },
        },
      },
      title: {
        display: true,
        text: title,
        align: 'start',
        color: '#374151',
        padding: {
          top: 0,
          left: 30,
          bottom: 10,
          right: 10,
        },
        font: {
          size: 18,
          weight: '500',

          family: 'inter',
        },
      },
      tooltip: {
        titleFont: {},
        bodyFont: {},
        displayColors: false,
        backgroundColor: 'grey',
        textColor: 'white',
        yAlign: 'bottom',
        callbacks: {
          labelTextColor: (ctx) => {
            return 'white'
          },
          title: (ctx) => {
            return `${ctx[0].label}`
          },
        },
      },
    },

    hover: {
      mode: 'label',
    },
    gridLines: {
      display: false,
    },
    scales: {
      x: {
        display: true,
        scaleLabel: {
          display: true,
        },
        grid: {
          display: false,
        },
      },
      'y-axis-1': {
        beginAtZero: true,
        border: {
          dash: [3, 5],
        },
        grid: {
          display: true,
          drawOnChartArea: true,
          drawTicks: true,
          lineWidth: 2,
        },
        ticks: {
          stepSize: 30000,
          callback: function (value, index, values) {
            if (value >= 1000) {
              return value / 1000 + 'K'
            }
            return value
          },
        },
      },
      'y-axis-2': {
        beginAtZero: true,
        border: {
          dash: [3, 5],
        },
        position: 'right',
        grid: {
          display: true,
          drawOnChartArea: true,
          drawTicks: true,
          lineWidth: 2,
        },
        ticks: {
          stepSize: 40000,
          callback: function (value, index, values) {
            if (value >= 1000) {
              return value / 1000 + 'K'
            }
            return value
          },
        },
      },
    },
  }
  return option
}

const generateDataSet = ({ type, label, values } = {}) => {
  const dataSet = {
    type: type || 'bar',
    label: label || 'Sales',
    data: values || [102189, 47748, 0, 104496, 163864, 155013, 147225],

    backgroundColor:
      type !== 'line'
        ? (context) => {
            const ctx = context.chart.ctx
            const gradient = ctx.createLinearGradient(0, 0, 0, 260)

            gradient.addColorStop(0, 'rgba(127, 86, 217, 1)') // Starting color
            gradient.addColorStop(0.2, 'rgba(127, 86, 217, 0.85)') // Slightly lighter
            gradient.addColorStop(0.4, 'rgba(127, 86, 217, 0.7)') // Even lighter
            gradient.addColorStop(0.6, 'rgba(127, 86, 217, 0.5)') // Half transparent
            gradient.addColorStop(0.8, 'rgba(127, 86, 217, 0.3)') // More transparent
            gradient.addColorStop(1, 'rgba(127, 86, 217, 0.1)') // Almost transparent

            return gradient
          }
        : 'rgba(127, 86, 217, 1)',

    borderWidth: type !== 'line' ? 0 : 3,
    yAxisID: type === 'bar' ? 'y-axis-1' : 'y-axis-2',
  }

  return dataSet
}

const chartData = ({ labels, datasets } = {}) => {
  const data = {
    labels: labels || getMonthsArray(),
    datasets: datasets || [],
  }

  return data
}

const SalesAndPaymentsChart = ({ title = '' }) => {
  const data = useAtomValue(dashboardAtom)
  const labels = getMonthsArray()

  const currentYear = moment().year()
  const currentMonthIndex = moment().month()

  // Initialize arrays with dynamic size based on the current month
  const salesValues = Array(currentMonthIndex + 1).fill(0)
  const paymentValues = Array(currentMonthIndex + 1).fill(0)

  const populateValues = (sourceData, targetArray, type) => {
    if (sourceData) {
      sourceData.forEach((obj) => {
        const key = Object.keys(obj)[0]
        targetArray[parseInt(key, 10) - 1] = obj[key] // Adjust for 1-based month index
      })
    }
  }

  if (data.sales && data.payments) {
    const salesData = data.sales[currentYear]
    const paymentsData = data.payments[currentYear]

    populateValues(salesData, salesValues, 'Sales')
    populateValues(paymentsData, paymentValues, 'Payments')
  }

  const option = generateChartOption({ type: 'bar', title: title })
  const paymentsDataSet = generateDataSet({
    type: 'bar',
    label: 'Sales',
    values: salesValues,
  })
  const purchasesDataSet = generateDataSet({
    type: 'line',
    label: 'Payments',
    values: paymentValues,
  })

  const paymentAndPurchaseChartData = chartData({
    labels: labels,
    datasets: [paymentsDataSet, purchasesDataSet],
  })

  return <Chart className="ml-2 pb-2" options={option} data={paymentAndPurchaseChartData} />
}

export default SalesAndPaymentsChart
