import React, { useState, useEffect } from 'react';
import osm from '@/assets/osm-provider';
import FitBounds from './FitBounds';
import { MapContainer, TileLayer, Marker, Popup, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import moment from 'moment';

const DefaultIcon = L.icon({
  iconUrl: './images/marker-icon.png',
  shadowUrl: './images/marker-shadow.png',
});

L.Marker.prototype.options.icon = DefaultIcon;

const MapComponent = ({ farmersList, onFarmerSelect, defaultFarmerName }) => {
  const [selectedFarmer, setSelectedFarmer] = useState(null);
  const [selectedMarker, setSelectedMarker] = useState(null);

  useEffect(() => {
    if (selectedMarker) {
      selectedMarker.openPopup(); // Auto open the popup for the selected marker
    }
  }, [selectedMarker]);

  const handleMarkerClick = (farmer, markerRef) => {
    setSelectedFarmer(farmer);
    setSelectedMarker(markerRef);
    onFarmerSelect(farmer);
  };

  const handleMarkerAdded = (farmer, markerRef) => {
    if (farmer.name == defaultFarmerName) {
      setSelectedMarker(markerRef);
    }
  };

  const handlePopupClosed = (farmer, markerRef) => {};

  return (
    <MapContainer
      className="h-screen w-screen"
      center={[15.8683264, 80.8812185]}
      zoom={10}>
      <TileLayer
        attribution={osm.maptiler.attribution}
        url={osm.maptiler.url}
      />
      <FitBounds farmersList={farmersList} />
      {farmersList.map((farmer) => {
        const markerRef = React.createRef();
        return (
          <Marker
            key={farmer.name}
            position={[farmer.location.lat, farmer.location.long]}
            ref={markerRef}
            eventHandlers={{
              click: () => handleMarkerClick(farmer, markerRef.current),
              add: () => handleMarkerAdded(farmer, markerRef.current),
              popupclose: () => handlePopupClosed(farmer, markerRef.current),
            }}>
            <Popup>
              <div className="text-base/6 font-semibold">{farmer.name}</div>
              <div className="text-xs/6 text-zinc-500">
                {farmer.shortAddress}
              </div>
              <div className="text-xs/6 text-zinc-500">
                last visited on{' '}
                {moment(farmer.visitedDate, 'DD MMM, YYYY hh:mm:ss').format(
                  'DD MMM, YYYY'
                )}
              </div>
            </Popup>
          </Marker>
        );
      })}
    </MapContainer>
  );
};

export default MapComponent;
