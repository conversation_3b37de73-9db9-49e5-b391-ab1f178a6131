# Zoho Payment API Integration Documentation

## Overview

This document provides comprehensive documentation for the Zoho Payment API integration in the AquaPartner application. The integration provides a complete payment processing solution with OAuth authentication, payment session management, webhook handling, and refund processing.

## Architecture

### Components

1. **Models**

   - `ZohoPaymentToken` - OAuth token management
   - `PaymentTransaction` - Transaction tracking and history

2. **Services**

   - `zohoPaymentService` - Core payment processing logic

3. **API Endpoints**
   - Authentication endpoints (`/api/zoho/auth/*`)
   - Payment endpoints (`/api/zoho/payments/*`)
   - Refund endpoints (`/api/zoho/refunds/*`)
   - Webhook endpoints (`/api/zoho/webhooks/*`)
   - Health check endpoints (`/api/zoho/health`)

## Setup Instructions

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```env
# Zoho OAuth Configuration
ZOHO_OAUTH_CLIENT_ID=your_client_id_here
ZOHO_OAUTH_CLIENT_SECRET=your_client_secret_here

# Zoho Payment Configuration
ZOHO_PAY_ACCOUNT_ID=your_account_id_here
ZOHO_PAYMENT_SESSION_URL=https://payments.zoho.in/api/v1/paymentsessions

# Webhook Configuration (Optional)
ZOHO_WEBHOOK_SECRET=your_webhook_secret_here

# Domain Configuration
NEXT_PUBLIC_DOMAIN=https://yourdomain.com
```

### 2. Zoho Developer Console Setup

1. Visit [Zoho Developer Console](https://accounts.zoho.in/developerconsole)
2. Create a "Self Client" application
3. Generate an authorization code with the following scopes:
   - `ZohoPay.payments.CREATE`
   - `ZohoPay.payments.READ`
   - `ZohoPay.refunds.CREATE`
   - `ZohoPay.refunds.READ`

### 3. Initial Token Setup

After obtaining the authorization code, call the setup endpoint:

```bash
POST /api/zoho/auth/setup
Content-Type: application/json

{
  "authorization_code": "your_authorization_code_here"
}
```

## API Endpoints

### Authentication

#### Setup Initial Tokens

```
POST /api/zoho/auth/setup
```

**Request Body:**

```json
{
  "authorization_code": "1000.dd7exxxxxxxxxxxxxxxxxxxxxxxx9bb8.b6c0xxxxxxxxxxxxxxxxxxxxxxxxdca4"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Zoho Payment tokens setup successfully",
  "data": {
    "token_id": "token_record_id",
    "expires_at": "2024-01-01T12:00:00.000Z",
    "scope": "ZohoPay.payments.CREATE,ZohoPay.payments.READ,ZohoPay.refunds.CREATE,ZohoPay.refunds.READ"
  }
}
```

#### Refresh Token

```
POST /api/zoho/auth/refresh
```

**Response:**

```json
{
  "success": true,
  "message": "Access token refreshed successfully",
  "data": {
    "token_refreshed": true,
    "expires_at": "2024-01-01T13:00:00.000Z"
  }
}
```

#### Check Token Status

```
GET /api/zoho/auth/refresh
```

### Payments

#### Create Payment Session

```
POST /api/zoho/payments/create-session
```

**Request Body:**

```json
{
  "amount": 100.5,
  "currency": "INR",
  "description": "Payment for Order #12345",
  "invoice_number": "INV-12345",
  "customer_id": "CUST-001",
  "customer_name": "John Doe",
  "customer_email": "<EMAIL>",
  "customer_phone": "+919876543210",
  "redirect_url": "https://yourapp.com/payment/success",
  "reference_id": "REF-12345",
  "meta_data": [
    { "key": "order_id", "value": "ORD-123" },
    { "key": "product_type", "value": "aquaculture" }
  ]
}
```

**Response:**

```json
{
  "success": true,
  "message": "Payment session created successfully",
  "data": {
    "payment_session_id": "2000000012001",
    "amount": "100.50",
    "currency": "INR",
    "description": "Payment for Order #12345",
    "invoice_number": "INV-12345",
    "created_time": 1708950672,
    "transaction_id": "local_transaction_id",
    "expires_in": "15 minutes"
  },
  "payment_session": {
    // Full Zoho payment session object
  }
}
```

#### Get Payment Status

```
GET /api/zoho/payments/status/{sessionId}
```

**Response:**

```json
{
  "success": true,
  "message": "Payment status retrieved successfully",
  "data": {
    "payment_session_id": "2000000012001",
    "status": "succeeded",
    "amount": "100.50",
    "currency": "INR",
    "description": "Payment for Order #12345",
    "invoice_number": "INV-12345",
    "created_time": 1708950672,
    "payments": [
      {
        "payment_id": "173000002314883",
        "status": "succeeded",
        "created_time": 1708950672
      }
    ],
    "transaction": {
      "id": "local_transaction_id",
      "customer_id": "CUST-001",
      "customer_name": "John Doe",
      "reference_id": "REF-12345",
      "created_at": "2024-01-01T12:00:00.000Z",
      "updated_at": "2024-01-01T12:05:00.000Z"
    }
  }
}
```

#### List Customer Payments

```
GET /api/zoho/payments/list?customer_id=CUST-001&page=1&limit=10&status=succeeded
```

**Response:**

```json
{
  "success": true,
  "message": "Transactions retrieved successfully",
  "data": {
    "transactions": [
      {
        "id": "local_transaction_id",
        "payment_session_id": "2000000012001",
        "payment_id": "173000002314883",
        "amount": 100.5,
        "currency": "INR",
        "description": "Payment for Order #12345",
        "invoice_number": "INV-12345",
        "status": "succeeded",
        "payment_method": "card",
        "customer_id": "CUST-001",
        "customer_name": "John Doe",
        "reference_id": "REF-12345",
        "created_at": "2024-01-01T12:00:00.000Z",
        "updated_at": "2024-01-01T12:05:00.000Z",
        "session_expires_at": "2024-01-01T12:15:00.000Z",
        "payment_completed_time": "2024-01-01T12:05:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    },
    "filters": {
      "customer_id": "CUST-001",
      "status": "succeeded",
      "from_date": null,
      "to_date": null
    }
  }
}
```

### Refunds

#### Create Refund

```
POST /api/zoho/refunds/create
```

**Request Body:**

```json
{
  "payment_id": "173000002314883",
  "amount": 50.25,
  "reason": "Product returned by customer",
  "reference_id": "REF-REFUND-123"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Refund created successfully",
  "data": {
    "refund_id": "174000002315001",
    "payment_id": "173000002314883",
    "amount": "50.25",
    "currency": "INR",
    "status": "pending",
    "reason": "Product returned by customer",
    "created_time": 1708951000,
    "reference_id": "REF-REFUND-123"
  },
  "refund": {
    // Full Zoho refund object
  }
}
```

### Webhooks

#### Payment Webhook Handler

```
POST /api/zoho/webhooks/payment
```

This endpoint automatically handles webhook events from Zoho Payments:

- `payment.succeeded` - Payment completed successfully
- `payment.failed` - Payment failed
- `payment.pending` - Payment is pending
- `payment.cancelled` - Payment was cancelled
- `payment_session.expired` - Payment session expired

**Webhook Configuration:**

- URL: `https://yourdomain.com/api/zoho/webhooks/payment`
- Method: POST
- Content-Type: application/json
- Signature Header: `x-zoho-webhook-signature` (if ZOHO_WEBHOOK_SECRET is configured)

### Health Check

#### System Health Check

```
GET /api/zoho/health
```

**Response:**

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "service": "Zoho Payment Integration",
  "version": "1.0.0",
  "status": "healthy",
  "checks": {
    "database": {
      "status": "healthy",
      "message": "Database connection successful"
    },
    "environment": {
      "status": "healthy",
      "message": "All required environment variables are set",
      "required_variables": ["ZOHO_PAY_ACCOUNT_ID", "ZOHO_OAUTH_CLIENT_ID", "ZOHO_OAUTH_CLIENT_SECRET", "MONGODB_URI"],
      "missing_variables": []
    },
    "zoho_auth": {
      "status": "healthy",
      "message": "Zoho authentication token is valid",
      "has_token": true
    },
    "zoho_api": {
      "status": "healthy",
      "message": "Zoho API is accessible"
    }
  },
  "summary": {
    "total_checks": 4,
    "healthy_checks": 4,
    "unhealthy_checks": 0
  },
  "configuration": {
    "account_id": "configured",
    "webhook_secret": "configured",
    "domain": "https://yourdomain.com"
  }
}
```

## Legacy Compatibility

The existing `/api/initiatePayment` endpoint has been updated to use the new Zoho Payment service while maintaining backward compatibility. It now:

1. Uses the new OAuth token management system
2. Creates proper transaction records
3. Provides enhanced error handling
4. Returns additional data while maintaining the original response format

## Error Handling

All endpoints provide consistent error responses:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "details": "Additional context or troubleshooting information"
}
```

Common error types:

- `Authentication Error` - OAuth token issues
- `Zoho Payment API Error` - Zoho API specific errors
- `Validation Error` - Invalid request data
- `Not Found` - Resource not found

## Rate Limits

Zoho Payments has the following rate limits:

- **Payments:** 600 requests per minute
- **Refunds:** 60 requests per minute

The integration automatically handles token refresh and includes proper error handling for rate limit scenarios.

## Security

1. **OAuth 2.0** - Secure token-based authentication
2. **Webhook Signature Verification** - Optional webhook signature validation
3. **Environment Variables** - Sensitive data stored securely
4. **Input Validation** - All inputs are validated before processing
5. **Error Logging** - Comprehensive logging without exposing sensitive data

## Testing

### Health Check

```bash
curl -X GET https://yourdomain.com/api/zoho/health
```

### Create Test Payment

```bash
curl -X POST https://yourdomain.com/api/zoho/payments/create-session \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 1.00,
    "description": "Test Payment",
    "invoice_number": "TEST-001",
    "customer_id": "TEST-CUSTOMER"
  }'
```

## Monitoring

Use the health check endpoint for monitoring:

- Database connectivity
- Zoho API accessibility
- Token validity
- Environment configuration

## Support

For issues related to:

- **Zoho API errors**: Check Zoho Payments documentation
- **Authentication issues**: Verify OAuth credentials and token setup
- **Webhook issues**: Verify webhook URL and signature configuration
- **Database issues**: Check MongoDB connection and models

## Migration Guide

If migrating from the old payment system:

1. Set up environment variables
2. Run initial token setup
3. Test with the health check endpoint
4. Update frontend to handle new response format (optional)
5. Configure webhooks in Zoho Payments dashboard
6. Monitor transactions using the new endpoints

The legacy `/api/initiatePayment` endpoint continues to work with enhanced functionality.
