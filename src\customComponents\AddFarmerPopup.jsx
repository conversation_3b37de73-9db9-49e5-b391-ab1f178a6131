import { Field, Label } from '@/components/fieldset'
import { Input } from '@/components/input'
import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/20/solid'
import { useState } from 'react'

const AddFarmerPopup = ({ onClose, onSubmit, setOpen, open }) => {
  const [email, setEmail] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit(email) // Trigger parent state update
  }

  return (
    <>
      <Dialog open={open} onClose={setOpen} className="relative z-10">
        <DialogBackdrop
          transition
          className="fixed inset-0 hidden bg-gray-200 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:block"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-stretch justify-center text-center md:items-center md:px-2 lg:px-4">
            {/* This element is to trick the browser into centering the modal contents. */}
            <span aria-hidden="true" className="hidden md:inline-block md:h-screen md:align-middle">
              &#8203;
            </span>
            <DialogPanel
              transition
              className="flex w-full transform text-left text-base transition data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:my-8 md:max-w-2xl md:px-4 data-[closed]:md:translate-y-0 data-[closed]:md:scale-95 lg:max-w-4xl"
            >
              <div className="relative flex w-full flex-col items-center overflow-hidden bg-white px-4 pb-8 pt-14 shadow-2xl sm:px-6 sm:pt-8 md:p-6 lg:p-8">
                <button
                  type="button"
                  onClick={() => setOpen(false)}
                  className="absolute right-4 top-4 text-gray-400 hover:text-gray-500 sm:right-6 sm:top-8 md:right-6 md:top-6 lg:right-8 lg:top-8"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon aria-hidden="true" className="h-6 w-6" />
                </button>
                <div className="flex h-screen w-full flex-1 flex-col">
                  <div className="-mx-4 px-4 pb-8 shadow-sm sm:mx-0 sm:rounded-lg sm:px-8 sm:pb-14 lg:col-span-2 lg:row-span-2 lg:row-end-2 xl:px-16 xl:pb-20">
                    <div className="flex flex-col items-start gap-x-6">
                      <h2 className="mb-4 text-xl">Add Your Farmer</h2>
                      <form onSubmit={handleSubmit}>
                        <Field className="mt-5">
                          <Label>Name</Label>
                          <Input name="name" />
                        </Field>
                        <Field className="mt-5">
                          <Label>Phone</Label>
                          <Input type="email" name="name" />
                        </Field>
                        <Field className="mt-5">
                          <Label>Email (Optional)</Label>
                          <Input type="email" name="name" />
                        </Field>
                        <div className="mt-5 flex justify-end">
                          <button
                            type="button"
                            onClick={() => setOpen(false)}
                            className="mr-2 rounded bg-gray-200 px-4 py-2"
                          >
                            Cancel
                          </button>
                          <button type="submit" className="rounded bg-indigo-600 px-4 py-2 text-white">
                            Add Farmer
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  )
}

export default AddFarmerPopup

/*

        <h2 className="mb-4 text-xl">Add Your Farmer</h2>
        <form onSubmit={handleSubmit}>
          <Field className="mt-5">
            <Label>Name</Label>
            <Input name="name" />
          </Field>
          <Field className="mt-5">
            <Label>Phone</Label>
            <Input type="email" name="name" />
          </Field>
          <Field className="mt-5">
            <Label>Email (Optional)</Label>
            <Input type="email" name="name" />
          </Field>
          <div className="mt-5 flex justify-end">
            <button type="button" onClick={onClose} className="mr-2 rounded bg-gray-200 px-4 py-2">
              Cancel
            </button>
            <button type="submit" className="rounded bg-indigo-600 px-4 py-2 text-white">
              Add Farmer
            </button>
          </div>
        </form>



*/
