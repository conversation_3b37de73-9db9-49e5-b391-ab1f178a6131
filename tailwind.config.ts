import type { Config } from 'tailwindcss'

const config: Config = {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/customComponents/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    // '!./src/customComponents/ProductCards.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        inter: ['Inter', { fontFeatureSettings: '"cv11"' }],
      },
      backgroundImage: {
        'dr-grow-banner-desktop': "url('/images/Dr_Grow_Banner_Desktop_1200x250.jpg')",
        'dr-grow-banner-mobile': "url('/images/Dr_<PERSON><PERSON>_Banner_Mobile_455x225.jpg')",
        'scheme-pattern': "url('/scheme/Bali_Resizes_Desktop_1200x104.jpg')",
        'scheme-pattern-tab': "url('/scheme/Bali_Resizes_Tab_800x106.jpg')",
        'scheme-pattern-mobile': "url('/scheme/Bali_Resizes_Mobile_400x117.jpg')",
        'bronze-coupon-gradient':
          'linear-gradient(95.46deg, #D6600B 2.8%, #F58C41 21.86%, #FBCFB0 47.11%, #FBD0B1 67.56%, #F58B40 87.39%, #F58B40 104.89%)',
        'silver-coupon-gradient':
          'linear-gradient(95.47deg, #717171 -0.22%, #9E9898 10.66%, #D6D6D6 26.65%, #D6D6D6 78.95%, #9E9E9E 93%, #9B9B9B 97.24%)',
        'gold-coupon-gradient':
          'linear-gradient(95.47deg, #DA8207 -0.22%, #DD9318 10.66%, #FFCB3C 26.65%, #FFCB3C 78.95%, #E39C1E 93%, #FBC63A 97.24%)',
      },
    },
  },
  plugins: [require('@tailwindcss/aspect-ratio')],
  layer: {
    utilities: {
      '.no-scrollbar::-webkit-scrollbar': {
        display: 'none',
      },
      '.no-scrollbar': {
        '-ms-overflow-style': 'none',
        'scrollbar-width': 'none',
      },
    },
  },
}
export default config
