export function getLastMonthLiquidationData(data) {
  if (!data) return 0 // Return null if the data is not provided

  // Get the current year and month
  const currentYear = new Date().getFullYear() // Get the current year
  const currentMonth = new Date().getMonth() + 1 // Get the current month (month is 0-indexed, so we add 1)

  // Find the entry for the current year
  const yearData = data?.find((entry) => entry.year == currentYear)

  if (!yearData) {
    return 0 // Return null if no data is found for the current year
  }

  const months = yearData.months

  const lastMonth = currentMonth - 1

  if (months[lastMonth]) {
    return months[lastMonth] // Return null if no valid month is found
  } else {
    return 0 // Return 0 if no valid month data is found
  }
}
