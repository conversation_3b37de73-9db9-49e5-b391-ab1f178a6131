// export function useClarity({ eventName = null, eventProperties = null, userId = null } = {}) {
//   useEffect(() => {
//     if (typeof window !== 'undefined' && window.clarity) {
//       if (userId) {
//         window.clarity('identify', userId)
//       }
//       if (eventName && eventProperties) {
//         window.clarity('set', eventName, eventProperties)
//       }
//     }
//   }, [eventName, eventProperties, userId])
// }

export function clarity(eventName = '', eventProperties = {}, userId = '') {
  console.log('Event Name: ' + eventName, 'Event Properties : ' + eventProperties, 'User Id : ' + userId)
  if (typeof window !== 'undefined' && window.clarity) {
    if (userId != '') {
      window.clarity('identify', userId)
      // console.log('updating clarity : ', userId)
    }
    if (eventName != '' && eventProperties != {}) {
      window.clarity('set', eventName, eventProperties)
    }
  }
}
