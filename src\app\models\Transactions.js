import moment from 'moment'
import { Schema, model, models } from 'mongoose'

const CustomerPaymentSchema = new Schema(
  {
    customerId: String,
    amount: String,
    paymentsDate: String,
    paymentId: String,
    paymentsMode: String,
    paymentsNumber: String,
    refundAmount: String,
    unusedAmount: String,
  },
  { collection: 'CustomerPayments' }
)

// Virtual to convert paymentsDate string to Date
CustomerPaymentSchema.virtual('paymentsDateAsDate').get(function () {
  return moment(this.paymentsDate, 'DD MMM YYYY').toDate()
})

// Enable virtuals to be included in JSON and Object responses
CustomerPaymentSchema.set('toObject', { virtuals: true })
CustomerPaymentSchema.set('toJSON', { virtuals: true })

const CustomerPayments = models.CustomerPayments || model('CustomerPayments', CustomerPaymentSchema)

export default CustomerPayments
