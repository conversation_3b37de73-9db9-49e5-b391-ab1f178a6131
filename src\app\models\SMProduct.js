import { Schema, model, models } from 'mongoose'

const SMProductSchema = new Schema(
  {
    createdBy: String,
    createdDateTime: Date,
    date: String,
    productId: String,
    productName: String,
    productQty: String,
    asmId: String,
    rmId: String,
    rowId: String,
    soId: String,
  },
  { collection: 'SMProduct' }
)

const SMProduct = models.SMProduct || model('SMProduct', SMProductSchema)

export default SMProduct
