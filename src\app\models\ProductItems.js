import { Schema, model, models } from 'mongoose'

const ProductItemsSchema = new Schema(
  {
    productId: String,
    productName: String,
    status: String,
    sku: String,
    usageUnit: String,
    weightInKgPerUnit: String,
    category: String,
    categoryType: String,
    salesPrice: Number,
  },
  { collection: 'ProductItems' }
)

const ProductItems = models.ProductItems || model('ProductItems', ProductItemsSchema)

export default ProductItems
