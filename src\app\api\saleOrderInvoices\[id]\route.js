import connectedDB from '@/app/config/database'
import SaleOrderInvoices from '@/app/models/SaleOrderInvoices'

// GET /api/saleOrderInvoices/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const saleOrderInvoices = await SaleOrderInvoices.find({
      salesOrderId: params.id,
    })

    // console.log(saleOrderInvoices);
    if (!saleOrderInvoices) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(saleOrderInvoices), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
