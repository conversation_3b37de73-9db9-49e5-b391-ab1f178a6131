'use client'
import {
  Dropdown,
  DropdownButton,
  DropdownDivider,
  DropdownItem,
  DropdownLabel,
  DropdownMenu,
} from '@/components/dropdown'
import { Navbar, NavbarItem, NavbarSection } from '@/components/navbar'
import {
  Sidebar,
  SidebarBody,
  SidebarFooter,
  SidebarHeader,
  SidebarItem,
  SidebarLabel,
  SidebarSection,
} from '@/components/sidebar'
import { SidebarLayout } from '@/components/sidebar-layout'
import { firebaseUserAtom, retailerAtom } from '@/customComponents/providers'
import { clarity } from '@/utils/clarity'
import { Button } from '@headlessui/react'
import { ArrowRightStartOnRectangleIcon, UserCircleIcon } from '@heroicons/react/16/solid'
import { ChartBarSquareIcon, DocumentIcon, QuestionMarkCircleIcon } from '@heroicons/react/20/solid'
import { BanknotesIcon, CubeIcon, CurrencyRupeeIcon, FaceSmileIcon, ViewColumnsIcon } from '@heroicons/react/24/solid'
import { useAtomValue, useSet<PERSON>tom } from 'jotai'
import { usePathname } from 'next/navigation'
import { auth } from '../config/firebaseConfig'

function handleSignOut() {
  auth.signOut()
}

function ButtonClicked({ page, user }: { page: string; user: string }) {
  clarity('pageViewed', '{ page: ' + page + ', phno: ' + user + ' }', '')
}

function AccountDropdownMenu({ anchor }: { anchor: 'top start' | 'bottom end' }) {
  return (
    <DropdownMenu className="min-w-64" anchor={anchor}>
      <DropdownDivider />
      <DropdownItem href="#" onClick={handleSignOut}>
        <ArrowRightStartOnRectangleIcon />
        <DropdownLabel>Sign out</DropdownLabel>
      </DropdownItem>
    </DropdownMenu>
  )
}

export function ApplicationLayout({
  //events,
  children,
}: {
  //events: Awaited<ReturnType<typeof getEvents>>
  children: React.ReactNode
}) {
  let pathname = usePathname()
  const customer = useAtomValue(retailerAtom)
  const setUser = useSetAtom(firebaseUserAtom)
  // console.log('Customer1 : ', customer)
  return (
    <SidebarLayout
      desktopNavbar={
        <div className="sticky top-0 z-10 hidden justify-between border-b-2 bg-[#F4F4F5] dark:bg-zinc-900 lg:flex">
          <div className="ml-7 flex flex-1 items-center">
            <p className="ml-2 flex-1 whitespace-nowrap text-sm font-semibold">
              {customer.companyName ? customer.companyName : ''}
            </p>
          </div>
          <div className="items-center">
            <Button color="blue" className="h-10 w-24" onClick={handleSignOut}>
              Sign Out
            </Button>
          </div>
        </div>
      }
      navbar={
        <Navbar className="top-0 z-50 bg-blue-500">
          <div className="flex w-full items-center justify-between">
            <div className="flex flex-1 items-center">
              <p className="ml-2 flex-1 overflow-hidden truncate whitespace-nowrap font-bold text-white">
                {customer.companyName && customer.companyName.length > 30
                  ? customer.companyName.slice(0, 30) + '...'
                  : customer.companyName}
              </p>
            </div>

            {/* Right Side: Navbar Section */}
            <NavbarSection className="flex-shrink-0">
              <Dropdown>
                <DropdownButton as={NavbarItem}>
                  <UserCircleIcon className="m-0 h-6 w-6 rounded-full bg-white" />
                </DropdownButton>
                <AccountDropdownMenu anchor="bottom end" />
              </Dropdown>
            </NavbarSection>
          </div>
        </Navbar>
      }
      sidebar={
        <Sidebar className="">
          <SidebarHeader className=""></SidebarHeader>

          <SidebarBody className="">
            <SidebarSection>
              <SidebarItem
                href="/"
                onClick={() => ButtonClicked({ page: 'Dashboard', user: customer?.mobileNumber ?? '' })}
                current={pathname === '/'}
              >
                <ChartBarSquareIcon color="white" />
                <SidebarLabel>Dashboard</SidebarLabel>
              </SidebarItem>

              <SidebarItem
                href="/accountStatement"
                onClick={() => ButtonClicked({ page: 'Account Statement', user: customer?.mobileNumber ?? '' })}
                current={pathname.startsWith('/accountStatement')}
              >
                <DocumentIcon color="white" />
                <SidebarLabel>Account Statement</SidebarLabel>
              </SidebarItem>

              <SidebarItem
                href="/billingAndPayments?page=Sales%20Orders"
                onClick={() => ButtonClicked({ page: 'Billings & Payments', user: customer?.mobileNumber ?? '' })}
                current={pathname.startsWith('/billingAndPayments')}
              >
                <CurrencyRupeeIcon color="white"></CurrencyRupeeIcon>
                <SidebarLabel>Billing & Payments</SidebarLabel>
              </SidebarItem>

              <SidebarItem
                href="/priceList"
                onClick={() => ButtonClicked({ page: 'priceList', user: customer?.mobileNumber ?? '' })}
                current={pathname.startsWith('/priceList')}
              >
                <BanknotesIcon color="white"></BanknotesIcon>
                <SidebarLabel>Price List</SidebarLabel>
              </SidebarItem>

              <SidebarItem
                href="/myFarmers"
                onClick={() => ButtonClicked({ page: 'My Farmers', user: customer?.mobileNumber ?? '' })}
                current={pathname.startsWith('/myFarmers')}
              >
                <FaceSmileIcon />
                <SidebarLabel>My Farmers</SidebarLabel>
              </SidebarItem>
              <SidebarItem
                href="/products?page=Catalogue"
                onClick={() => ButtonClicked({ page: 'Products', user: customer?.mobileNumber ?? '' })}
                current={pathname.startsWith('/products')}
              >
                <CubeIcon />
                <SidebarLabel>Products</SidebarLabel>
              </SidebarItem>
              <SidebarItem
                href="/inventory"
                onClick={() => ButtonClicked({ page: 'Stocks', user: customer?.mobileNumber ?? '' })}
                current={pathname.startsWith('/inventory')}
              >
                <ViewColumnsIcon />
                <SidebarLabel>Stocks</SidebarLabel>
              </SidebarItem>
            </SidebarSection>
          </SidebarBody>
          <SidebarFooter className="border-none">
            <SidebarItem
              href="/support"
              onClick={() => ButtonClicked({ page: 'Help / Support', user: customer?.mobileNumber ?? '' })}
              current={pathname.startsWith('/support')}
            >
              <QuestionMarkCircleIcon />
              <SidebarLabel>Help / Support</SidebarLabel>
            </SidebarItem>
          </SidebarFooter>
        </Sidebar>
      }
    >
      {children}
    </SidebarLayout>
  )
}
