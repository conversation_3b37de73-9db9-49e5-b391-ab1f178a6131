import { Schema, model, models } from 'mongoose';

const ProductSchema = new Schema(
  {
    itemId: String,
    itemName: String,
    Status: String,
    SKU: String,
    purchasePrice: String,
    usageUnit: String,
    weightInKgPerUnit: Number,
    Category: String,
    categoryType: String,
    hsnSac: Number,
    salesPrice: String,
    MRP: String,
    taxExemptionId: String,
    shelfLifeMonth: Number,
    createdTime: Date,
    lastModifiedTime: Date,
  },
  { collection: 'Items' }
);

const Products = models.Items || model('Items', ProductSchema);

export default Products;
