import { Schema, model, models } from 'mongoose';

// Define the schema for dues
const DuesSchema = new Schema(
  {
    ageTier: { type: String, required: true }, // e.g., "16-30"
    totalAmount: { type: Number, required: true }, // Total due amount
  },
  { _id: false }
);

const DashboardSchema = new Schema(
  {
    customerId: { type: String, required: true }, // Customer ID
    sales: { type: Map, of: [Map], default: {} }, // Empty sales object (yearly sales can be added later)
    payments: { type: Map, of: [Map], default: {} }, // Empty payments object (yearly payments can be added later)
    dues: { type: [DuesSchema], default: [] }, // Empty array for dues
    salesReturn: { type: Number, required: true }, // Total sales return
  },
  { collection: 'Dashboard' }
);

const Dashboard = models.Dashboard || model('Dashboard', DashboardSchema);

export default Dashboard;
