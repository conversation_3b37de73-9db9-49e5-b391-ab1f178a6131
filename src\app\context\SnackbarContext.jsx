'use client'
import CustomSnackBar from '@/customComponents/common/Toast'
import { createContext, useContext, useState } from 'react'

const SnackbarContext = createContext()

export const useSnackbar = () => {
  return useContext(SnackbarContext)
}

export const SnackbarProvider = ({ children }) => {
  const [isSnackBarVisible, setSnackBarVisible] = useState(false)
  const [snackBarMessage, setSnackBarMessage] = useState('')
  const [snackBarType, setSnackBarType] = useState('success') // 'success' or 'error'

  const showSnackBar = (message, type = 'success') => {
    setSnackBarMessage(message)
    setSnackBarType(type)
    setSnackBarVisible(true)
  }

  const hideSnackBar = () => {
    setSnackBarVisible(false)
  }

  return (
    <SnackbarContext.Provider value={{ showSnackBar, hideSnackBar }}>
      {children}
      {/* Snackbar component rendered at the top level so it's available globally */}
      <CustomSnackBar
        message={snackBarMessage}
        type={snackBarType}
        isVisible={isSnackBarVisible}
        onClose={hideSnackBar}
      />
    </SnackbarContext.Provider>
  )
}
