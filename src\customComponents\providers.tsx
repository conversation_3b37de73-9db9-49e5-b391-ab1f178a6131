'use client'

import { ConfirmationR<PERSON>ult, RecaptchaVerifier, User } from 'firebase/auth'
import { Provider, atom } from 'jotai'
import { atomWithStorage, createJSONStorage } from 'jotai/utils'
import React, { ReactNode } from 'react'

// Define the Retailer interface
interface Retailer {
  customerId: string
  customerCode: string
  customerName: string
  contactName: string
  GSTNo: string
  companyName: string
  mobileNumber: string
  Email: string
  billingAddress: string
  businessVertical: string
  customerType: string
  Status: string
  rowId: string
}

// Atom for retailer data with local storage persistence
const retailerAtom = atomWithStorage<Retailer>('customer', {
  customerId: '',
  customerCode: '',
  customerName: '',
  contactName: '',
  GSTNo: '',
  companyName: '',
  mobileNumber: '',
  Email: '',
  billingAddress: '',
  businessVertical: '',
  customerType: '',
  Status: '',
  rowId: '',
})

interface PriceListType {
  state: string
  date: string
  prices: any[]
}

// Atoms for various data, typed as Record<string, any>
const dashboardAtom = atomWithStorage<Record<string, any>>('dashboardData1', {})
const salesOrdersAtom = atomWithStorage<Record<string, any>>('salesOrderData', {})
const invoicesAtom = atomWithStorage<Record<string, any>>('invoicesData', {})
const customerPaymentsAtom = atomWithStorage<Record<string, any>>('customerPaymentsData', {})
const creditNotesAtom = atomWithStorage<Record<string, any>>('creditNotesData', {})
const duesAtom = atomWithStorage<Record<string, any>>('duesData', {})
const customerStatementAtom = atomWithStorage<Record<string, any>>('customerStatementData', [])
const farmerVisitsAtom = atomWithStorage<Record<string, any>>('visitData', {})
const smrReportAtom = atomWithStorage<Record<string, any>>('smrReportData', {})
const salesMasterDataAtom = atomWithStorage<Record<string, any>>('salesMasterData', {})
const productCatalogueAtom = atomWithStorage<Record<string, any>>('productCatalogue', {})
const carouselDataAtom = atomWithStorage<Record<string, any>>('carouselData', {})
const schemeDataAtom = atomWithStorage<Record<string, any>>('schemeData', {})
const supportPersionsAtom = atomWithStorage<Record<string, any>>('supportPersions', [])

// Atoms for Firebase instances (do not persist in local storage)
const recaptchaVerifierAtom = atom<RecaptchaVerifier | null>(null)
const confirmationResultAtom = atom<ConfirmationResult | null>(null)
const firebaseUserAtom = atom<User | null>(null)
const loadingAtom = atom<boolean | true>(true)
const errorAtom = atomWithStorage<Record<string, any>>('errorMessage', {})
const selectedTabAtom = atom(0)

// Create custom storage with SSR check
const storage = createJSONStorage<PriceListType>(() => {
  // Check if window is defined (client-side)
  if (typeof window !== 'undefined') {
    return window.localStorage
  }
  // Return dummy storage for server-side
  return {
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
  }
})

// Configure atom with storage directly
const priceListAtom = atomWithStorage<PriceListType>(
  'priceList',
  {
    state: '',
    date: '',
    prices: [],
  },
  storage
)

// Export the atoms
export {
  carouselDataAtom,
  confirmationResultAtom,
  creditNotesAtom,
  customerPaymentsAtom,
  customerStatementAtom,
  dashboardAtom,
  duesAtom,
  errorAtom,
  farmerVisitsAtom,
  firebaseUserAtom,
  invoicesAtom,
  loadingAtom,
  priceListAtom,
  productCatalogueAtom,
  recaptchaVerifierAtom,
  retailerAtom,
  salesMasterDataAtom,
  salesOrdersAtom,
  schemeDataAtom,
  selectedTabAtom,
  smrReportAtom,
  supportPersionsAtom,
}

// Provider component with typings
interface ProvidersProps {
  children: ReactNode
}

export const Providers: React.FC<ProvidersProps> = ({ children }) => {
  return <Provider>{children}</Provider>
}
