'use client'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import { Checkbox } from '@/customComponents/CheckBox'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { indianCurrencyFormat } from '@/utils/formats'
import { useFetchDuesDataByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import _ from 'lodash'
import moment from 'moment'
import { useEffect, useState } from 'react'

export const DuesPage = () => {
  const customer = useAtomValue(retailerAtom)
  const duesData = useFetchDuesDataByCustomerId(customer.customerId)

  const [duesAgingCheckedItems, setDuesAgingCheckedItems] = useState({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (duesData?.results) {
      setLoading(false)
      var checkedItem = {}
      _.map(duesData.results, (item) => {
        checkedItem[item.aging] = true
      })

      // Set checked items only once when data is available
      setDuesAgingCheckedItems(checkedItem)
    }
  }, [duesData])

  const handleCheckboxChange = (event, label) => {
    setCheckedItems((prevState) => ({
      ...prevState,
      [label]: event.target.checked,
    }))
  }

  const handleNewCheckboxChange = (event, label) => {
    setDuesAgingCheckedItems((prevState) => ({
      ...prevState,
      [label]: event.target.checked,
    }))
  }

  return (
    <>
      {loading && <LoadingScreen />}
      {duesData.results && (
        <>
          <div className="sm:flex sm:flex-row sm:space-x-4">
            <h2 className="my-2 text-lg font-semibold">Due Aging (in Days)</h2>
            {Object.keys(duesAgingCheckedItems).map((val, index) => (
              <Checkbox
                key={val}
                label={val}
                onChange={(event) => {
                  handleNewCheckboxChange(event, val)
                }}
                checked={duesAgingCheckedItems[val] || false}
              />
            ))}
          </div>
          <Table className="mt-2 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
            <TableHead>
              <TableRow>
                <TableHeader>Date</TableHeader>
                <TableHeader>ID</TableHeader>
                <TableHeader className="text-right">Amount</TableHeader>
                <TableHeader className="text-right">Due Days</TableHeader>
                <TableHeader>Aging</TableHeader>
              </TableRow>
            </TableHead>
            <TableBody>
              {duesData.results
                .filter((dues) => duesAgingCheckedItems[dues.aging] === true)
                .flatMap((dues) => dues.data)
                .map((data, index) => (
                  <TableRow key={index}>
                    <TableCell>{data && moment(data.invoiceDate).format('DD-MM-YY')}</TableCell>
                    <TableCell>{data && data.invoiceNumber}</TableCell>

                    <TableCell className="text-right">{data && indianCurrencyFormat(data.due)}</TableCell>
                    <TableCell className="text-right">{data && data.dueDays}</TableCell>
                    <TableCell>{data && data.aging1}</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </>
      )}
    </>
  )
}
