import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp({
credential: admin.credential.cert({
      projectId: "aquaconnect-partner-prod",
      clientEmail: "<EMAIL>",
      privateKey: "-----B<PERSON>IN PRIVATE KEY-----\nMIIEugIBADANBgkqhkiG9w0BAQEFAASCBKQwggSgAgEAAoIBAQCpAIzHJvFe2fmX\nOriHUvrzR7H3SHL1SOnBLqDV+ZLixj4c29HNCX7U7gGLFuvAM7X4BbFsjv75SYDw\nu8S2pq7HTnRWx2Kkg8+zQQUu8Hav7wUpTCoOfoezRqT3HTRyxFRjRZ5oh36Mlidg\nbJ4beqLYfOqx1HgH7dBbhj2cyEHeyRJLhBW7kQSwEyjZ2N7OMrbPuZEf3IBbTdNK\nIaBt7XRu4NBYh1120tMiKIagR3tFeH+6/hw3yf67hPiq6xS63k3CczUIgjLvhyAw\nouexgViEiVWBPZD1oXNoJQ95Zmg2Sr2A/fOK2AksUfRMICdQvJBqAnqWBRv9fqTW\no8NF4VAHAgMBAAECgf96scbwhcL/0K4aZhmR3D7m8rhmGA6CTpGvigYV5PaaP1KP\noQdm1DTSqtPhuTABEPd49KkkAqzQ4UMo8JbgBCMo2INUxkfXKG00bUkxgauEoUwd\n+HbZM5WabYrEJks2veTPUimKlWZKqrh9/4HucT7tWp6/6e6oZUgomAPZ89Jw6qSd\nYoCAtkBpzv+mjzPnGKLvjZXeO7QBtt2qbngW7G4DqdiiBYpJPc4HQkk1aknEUzaz\nnaYMtTEma8V1y/MRD/LgYMuOEN6B6FPtdQcslRptFDvdk7JlAk1wD27HhGXvH+lo\ny1PwBBkKDGfLfXJALrBELUiPTIjyJex5QgB1FLkCgYEAyVjBnta5QpZJxi3F1ZJf\nRxgc+Tv9UMHcN0t7DvS0atQ1hAWCfIpkEDmFSGC1IsfKdZ5qhhci4yfHTvPBJDPn\nY+0UkCU1OMBQtOarLDjiSbLJHLEh8ony/sPGzwuqgDhAmKT/v7A5jH6erfGdWasu\ndmeM+IwcHdT133OzlE7kx8UCgYEA1uA5jBp2scr+u8R+Fcfou24my9kaN7rpB01X\n3t/KU60JjBBMdj4ybIxDkpwSi/24fsKwnqfvL9f2o2LA47qAoeY8RI/dN5MWLbrR\nOem2F3cufStG68qJfHzoXffvzmF57QcY7pDyQ1eCdAYLB9rNJvgEyaH/4nhFEHvf\n+82M6VsCgYB+ZVZe1SLFdzzmyNM5iEKBidSebRdPe/M3Tw34TIt5yK+zqhXJAsNg\nIAvYbMUpCUzCW2k5tgpzmQQYPxLPHDo0056mAoWPk8LWXTu1bhw38aEoftX1wL2n\nStViIkutZCLA8EuQVRs7ZD1Oqq+Bm+F1+PlZCfOYkpiq5qIkBwa9lQKBgFcmqJCX\npy6ja8ASiSUiaNWkbp3dV1HlQGCS0SH3WKOpmiax8aE/xsnZ9go/vRIEfVEEfAhN\niqi4CjPoeN2kpkmEDUutogbJZIR1siiwDwfjcfqYk/JMuojATKrYRe5KAN7PMyCH\nr4r6slGGpfCwoOa+T4rMZvGe4Dw4UfdaPl3jAoGAaUswNlceRpm9Tqp6+kDxGPPD\n8jB4lewKMpDUWoeWklSF6vIrc4Je496BrOGEBp4mnZfifv/4R14NDLCv5n0JJHwd\nnfvwGn86UOTRxMKec45lSKtq/G7h0pLOXIwmUclU7KXNVYpNklTnvinA0km1fBOr\nRNspkCT895iSKJV7tAs=\n-----END PRIVATE KEY-----\n".replace(/\\n/g, '\n'),
    }),
  });
}

// Verify Firebase ID token
export async function verifyIdToken(token) {
  try {
    const decodedToken = await admin.auth().verifyIdToken(token);
    return { uid: decodedToken.uid, authenticated: true };
  } catch (error) {
    return { authenticated: false, error: error.message };
  }
}

// Middleware for App Router
export async function withAuth(request) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return { authenticated: false, error: 'Authorization header missing' };
    }

    // Extract token
    const token = authHeader.split(' ')[1];
    if (!token) {
      return { authenticated: false, error: 'Token not found' };
    }

    // Verify token
    const { authenticated, uid, error } = await verifyIdToken(token);
    
    if (!authenticated) {
      return { authenticated: false, error: error };
    }

    // Return authenticated status and user ID
    return { authenticated: true, uid };
  } catch (error) {
    return { authenticated: false, error: 'Internal server error during authentication' };
  }
}
