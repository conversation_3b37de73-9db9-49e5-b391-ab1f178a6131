import { PhoneIcon } from '@heroicons/react/24/solid'

// ContactCard.js
export const ContactCard = ({ person }) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
      <div
        key={person?.email}
        className="relative flex items-center space-x-3 py-2 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:border-gray-400"
      >
        <div className="flex-shrink-0">
          {person && person?.userProfile !== 'Support' ? (
            <img
              alt=""
              src={`https://firebasestorage.googleapis.com/v0/b/aquaconnect-partner-prod.appspot.com/o/users%2F${person.empId.toLowerCase().trim()}.jpg?alt=media`}
              className="size-12 rounded-full"
            />
          ) : (
            <PhoneIcon className="size-10 rounded-full" />
          )}
        </div>
        <div className="min-w-0 flex-1">
          <a href="#" className="focus:outline-none">
            <span aria-hidden="true" className="absolute inset-0" />
            <p className="text-sm font-medium text-gray-900">{person?.userName.split('-')[1]?.trim() ?? ''}</p>
            <p className="truncate text-sm text-gray-500">
              {person?.userProfile !== 'Support' ? person?.mobile.slice(-10) ?? '' : person?.mobile}
            </p>
          </a>
        </div>
      </div>
    </div>
  )
}
