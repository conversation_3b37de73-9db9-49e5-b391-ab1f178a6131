import { Schema, model, models } from 'mongoose'

const UsersSchema = new Schema(
  {
    rowId: String,
    customerName: String,
    email: String,
    status: String,
    mobileNumber: String,
    profile: String,
    userId: String,
    modifiedTime: Date,
    empId: String,
    soId: String,
  },
  { collection: 'Users' }
)

const Users = models.Users || model('Users', UsersSchema)

export default Users
