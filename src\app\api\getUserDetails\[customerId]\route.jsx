import connectedDB from '@/app/config/database'
import Customer from '@/app/models/Customer'
import Users from '@/app/models/Users'

// GET /api/getUserDetails/:customerId
export const GET = async (request, { params }) => {
  try {
    await connectedDB()

    // Debug params and customer query
    console.log('Params:', params)

    const customer = await Customer.aggregate([
      {
        $match: { customerId: params.customerId },
      },
      {
        $project: { _id: 0 },
      },
    ])

    if (customer.length > 0) {
      const soId = customer[0].soId
      const asmId = customer[0].asmId

      console.log('ASM Id ', asmId, 'AO Id', soId)

      const userDetails = await Users.aggregate([
        {
          $match: {
            $or: [{ rowId: asmId }, { rowId: soId }],
          },
        },
        {
          $project: {
            _id: 0, // Exclude _id
            key: '$profile', // Dynamic key
            value: {
              userName: '$customerName',
              userId: '$userId',
              empId: '$empId',
              mobile: '$mobileNumber',
              email: '$email',
              userProfile: '$profile',
            },
          },
        },
        {
          $group: {
            _id: null, // Group into a single document
            keyValuePairs: {
              $push: { k: '$key', v: '$value' }, // Create key-value pairs
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: { $arrayToObject: '$keyValuePairs' }, // Convert array of key-value pairs into an object
          },
        },
      ])

      return new Response(JSON.stringify({ results: userDetails }), { status: 200 })
    } else {
      return new Response(JSON.stringify({ results: [] }), { status: 200 })
    }
  } catch (error) {
    console.error('Error:', error)
    return new Response(JSON.stringify({ error: error.message }), { status: 500 })
  }
}

