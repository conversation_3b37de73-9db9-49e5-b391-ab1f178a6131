'use client'

import { ProductDetailView } from './productDetailsView'

import { Badge } from '@/components/badge'
import { Divider } from '@/components/divider'
import { retailerAtom } from '@/customComponents/providers'
import { useFetchProductCatalogue } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import { useState } from 'react'

export const CataloguePage = () => {
  const [product, setProduct] = useState(null)
  const [showProductDetails, setShowProductDetails] = useState(false)
  const customer = useAtomValue(retailerAtom)
  const productCatalogue = useFetchProductCatalogue(customer.customerId)

  const producctClicked = (product) => {
    setProduct((prev) => product)
    setShowProductDetails(true)
  }

  return (
    <div className="flex min-h-screen flex-row overflow-y-auto scroll-smooth">
      <div className="flex w-screen flex-col">
        <ul name="productList" className="mt-0 flex-1" key={'products'}>
          {productCatalogue.results &&
            productCatalogue.results.map((brand, brandIndex) => {
              return brand.products.map((product, index) => {
                return (
                  <div key={index} onClick={() => producctClicked(product)} className="cursor-pointer">
                    <li key={brandIndex}>{brandIndex > 0 && <Divider soft={brandIndex > 0} />}</li>
                    <div key={brand.name + brandIndex} className={`flex items-center justify-between`}>
                      <div className="flex gap-6 py-4">
                        <div className="w-32 shrink-0">
                          <img
                            className="h-32 w-32 rounded-lg border border-gray-100 bg-gray-50 object-cover p-2"
                            src={product.productImage}
                            alt=""
                          />
                        </div>
                        <div className="flex flex-col justify-center">
                          <div className="">
                            {' '}
                            <h2 className="text-sm font-semibold uppercase text-gray-400">{brand.name}</h2>{' '}
                          </div>
                          <div className="flex items-center gap-2 text-lg font-semibold uppercase text-gray-700">
                            <h1>{product.productName}</h1>
                            {brand.name == 'Dr.Grow' && (
                              <Badge className="text-xs uppercase" color={'yellow'}>
                                {'Best Seller'}
                              </Badge>
                            )}
                          </div>
                          <div className="text-base text-gray-500">{product.tagLine}</div>
                          <div className="flex items-center gap-4"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })
            })}
        </ul>

        {showProductDetails && (
          <ProductDetailView open={showProductDetails} setOpen={setShowProductDetails} product={product} />
        )}
      </div>
    </div>
  )
}
