import { Schema, model, models } from 'mongoose'

// Define the schema for dues
const PriceSchema = new Schema(
  {
    count: { type: String }, // e.g., "16-30"
    price: { type: String }, // Total due amount
  },
  { _id: false }
)

const PriceListSchema = new Schema(
  {
    state: { type: String, required: true }, // Customer ID
    date: { type: String }, // Empty sales object (yearly sales can be added later)
    prices: { type: [PriceSchema], default: [] }, // Empty array for dues
    salesReturn: { type: Number, required: true }, // Total sales return
  },
  { collection: 'PriceList' }
)

const PriceList = models.PriceList || model('PriceList', PriceListSchema)

export default PriceList
