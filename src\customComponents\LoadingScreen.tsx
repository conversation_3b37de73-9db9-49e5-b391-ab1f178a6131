import React from 'react'

const LoadingScreen: React.FC = () => {
  return (
    <>
      <style jsx>{`
        .loader {
          width: 50px;
          padding: 8px;
          aspect-ratio: 1;
          border-radius: 50%;
          background: #0810f7;
          --_m: conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box;
          -webkit-mask: var(--_m);
          mask: var(--_m);
          -webkit-mask-composite: source-out;
          mask-composite: subtract;
          animation: l3 1s infinite linear;
        }
        @keyframes l3 {
          to {
            transform: rotate(1turn);
          }
        }
      `}</style>
      <div className="fixed inset-0 z-10 flex items-center justify-center bg-white bg-opacity-75">
        <div className="loader"></div>
      </div>
    </>
  )
}

export default LoadingScreen
