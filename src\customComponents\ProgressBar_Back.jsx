export default function ProgressBar() {
  return (
    <>
      <div className="mx-auto rounded-lg bg-white p-6 shadow-md">
        {/* <div className="relative rounded border border-green-400 bg-green-100 px-4 py-3 text-green-700" role="alert">
          <strong className="font-bold">Great Job!</strong>
          <span className="block sm:inline">{`You've reached the Bronze level. Keep going to reach Silver!`}</span>
        </div> */}
        <h3 className="mb-4 mt-5 text-lg font-semibold text-gray-700">Silver Level Progress</h3>

        {/* <!-- Combined Progress Bar --> */}
        <div className="relative h-4 overflow-hidden rounded-full bg-gray-200">
          {/* <!-- Overall Progress --> */}
          <div className="absolute h-full w-3/4 bg-blue-500"></div>
          {/* <!-- Dr.Grow Products Progress --> */}
          <div className="absolute h-full w-2/3 bg-green-500"></div>
        </div>

        <div className="mt-2 flex justify-between text-sm">
          <span className="text-green-500">₹2,00,000 / ₹3,00,000</span>
          <span className="text-blue-500">₹45,000 / ₹60,000 (Dr.Grow)</span>
        </div>
      </div>
    </>
  )
}
