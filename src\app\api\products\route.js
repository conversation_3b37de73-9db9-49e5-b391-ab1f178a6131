import connectedDB from '@/app/config/database'
import CustomerInterest from '@/app/models/CustomerInterest'
import Products from '@/app/models/Products'
import moment from 'moment-timezone'

// GET /api/products
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const products = await Products.find({})

    // console.log(products);
    if (!products) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(products), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}

export const POST = async (req) => {
  try {
    await connectedDB()
    const body = await req.json()
    body.datetime = moment.utc().tz('Asia/Kolkata').toISOString();
    body.source = "PartnerApp"
    const result = await CustomerInterest.insertMany([body])
    console.log(body)
// const utcToIndian = 

    
    console.log(datetime); //January 1st 2013, 9:00:00 am

    console.log('Result : ', result)
    //if (!products) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify({ result: 'Success' }), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(JSON.stringify({ error: 'error', message: 'try again.' }), { status: 500 })
  }
}
