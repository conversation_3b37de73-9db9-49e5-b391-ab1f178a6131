'use client'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import LoadingScreen from '@/customComponents/LoadingScreen'
import { retailerAtom } from '@/customComponents/providers'
import { indianCurrencyFormat } from '@/utils/formats'
import { useFetchCreditNotesByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useEffect, useState } from 'react'

export const CreditNotesPage = () => {
  const [loading, setLoading] = useState(true)
  const customer = useAtomValue(retailerAtom)
  const creditNotes = useFetchCreditNotesByCustomerId(customer.customerId)

  useEffect(() => {
    if (creditNotes?.results) {
      setLoading((prev) => false)
    }
  }, [creditNotes])

  return (
    <>
      {loading && <LoadingScreen />}
      <Table className="mt-2 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
        <TableHead>
          <TableRow>
            <TableHeader>Date</TableHeader>
            <TableHeader>Credit Note Number</TableHeader>
            <TableHeader className="text-right">Total</TableHeader>
            <TableHeader>Category</TableHeader>
            <TableHeader>Type</TableHeader>
          </TableRow>
        </TableHead>
        <TableBody className="pl-4">
          {creditNotes.results &&
            creditNotes.results.map((creditNote, index) => (
              <TableRow key={index}>
                <TableCell>{moment(creditNote.date1).format('DD-MM-YYYY')}</TableCell>
                <TableCell>{creditNote.creditNoteNumber}</TableCell>
                <TableCell className="text-right">{indianCurrencyFormat(creditNote.amount)}</TableCell>
                <TableCell>{creditNote.itemCategory}</TableCell>
                <TableCell>{creditNote.category}</TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </>
  )
}
