import { Schema, model, models } from 'mongoose'

const CustomerInterestSchema = new Schema(
  {
    customerId: String,
    name: String,
    mobile: String,
    productId: String,
    productName: String,
    datetime: Date,
    source: String
  },
  { collection: 'InterestedProducts' }
)

const CustomerInterest = models.InterestedProduccts || model('InterestedProducts', CustomerInterestSchema)

export default CustomerInterest
