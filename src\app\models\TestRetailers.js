import { Schema, model, models } from 'mongoose'

const TestRetailersSchema = new Schema(
  {
    customerId: String,
    customerName: String,
    Email: String,
    mobileNumber: String,
    companyName: String,
    gstNo: String,
    address: String,
    customerCode: String,
    businessVertical: String,
  },
  { collection: 'TestRetailers' }
)

const TestRetailers = models.TestRetailers || model('TestRetailers', TestRetailersSchema)

export default TestRetailers
