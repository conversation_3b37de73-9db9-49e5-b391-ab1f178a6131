import connectedDB from '@/app/config/database'
import PriceList from '@/app/models/PriceList'

// GET /api/priceList/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const pricelist = await PriceList.find().sort({ state: 1 })

    if (!pricelist) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: pricelist }), { status: 200 })
  } catch (error) {
    return new Response(error, { status: 500 })
  }
}
