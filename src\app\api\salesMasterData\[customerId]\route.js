import connectedDB from '@/app/config/database'
import SalesMasterData from '@/app/models/SalesMasterData'

// GET /api/salesMasterData/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const salesMasterData = await SalesMasterData.find({
      customerId: params.customerId,
    }).sort({ lastModifiedTime: -1 })
    if (!salesMasterData) return new Response(JSON.stringify([]), { status: 200 })
    return new Response(JSON.stringify(salesMasterData), { status: 200 })
  } catch (error) {
    console.log(error)
    return new Response(error, { status: 500 })
  }
}
