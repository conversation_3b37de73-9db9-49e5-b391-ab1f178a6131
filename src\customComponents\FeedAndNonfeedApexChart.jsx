'use client'
import { getWeeksInMonth } from '@/utils/date'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useCallback, useEffect, useState } from 'react'
import { getMonthsArray } from '../utils/extras'
import Apex<PERSON>hart from './ApexChart'
import { dashboardAtom } from './providers'

export const FeedAndNonfeedApexChart = ({ selectedValue }) => {
  const data = useAtomValue(dashboardAtom)
  const [labels, setLabels] = useState(() => getMonthsArray())
  const [currentYear] = useState(() => moment().year())
  const [currentMonthIndex] = useState(() => moment().month()) // Returns current month index (0 for Jan, 11 for Dec)

  // Initialize arrays with dynamic size based on the current month
  const [feedPurchaseValues, setFeedPurchaseValues] = useState(() => Array(currentMonthIndex + 1).fill(0))
  const [nonFeedPurchaseValues, setNonFeedPurchaseValues] = useState(() => Array(currentMonthIndex + 1).fill(0))

  const populateMonthValues = useCallback(
    (sourceData, setFeedPurchase, setNonFeedPurchase, type) => {
      const feedPurchaseValues = Array(currentMonthIndex + 1).fill(0)
      const nonFeedPurchaseValues = Array(currentMonthIndex + 1).fill(0)

      if (data.categoryTypeSales) {
        // Loop over each categoryTypeSales object
        sourceData.categoryTypeSales.forEach((obj) => {
          const currentYearSales = obj.yearlySales[currentYear]

          // Only process if currentYearSales exists
          if (currentYearSales) {
            currentYearSales.forEach((sale) => {
              // Use a single switch block to reduce duplication for both Feed and Non Feed
              const position = Object.keys(sale)[0]
              const totalAmount = sale[position]['totalPayableAmount']
              switch (obj.categoryType) {
                case 'Feed':
                  feedPurchaseValues[position - 1] = totalAmount // Adjust month index
                  break
                case 'Non Feed':
                  nonFeedPurchaseValues[position - 1] = totalAmount // Adjust month index
                  break
              }
            })
          }
        })
        setFeedPurchase(feedPurchaseValues)
        setNonFeedPurchase(nonFeedPurchaseValues)
      }
    },
    [data, currentYear, currentMonthIndex]
  )

  const populateWeekValues = useCallback(
    (sourceData, setFeedPurchase, setNonFeedPurchase, weeksLength, selectedMonth) => {
      const feedPurchaseValues = Array(weeksLength).fill(0)
      const nonFeedPurchaseValues = Array(weeksLength).fill(0)

      if (data.categoryTypeSales) {
        // Loop over each categoryTypeSales object
        sourceData.categoryTypeSales.forEach((obj) => {
          const currentYearSales = obj.yearlySales[currentYear]

          // Only process if currentYearSales exists
          if (currentYearSales) {
            const selectedMonthData = currentYearSales.find((sale) => {
              return sale[selectedMonth]
            })
            if (selectedMonthData) {
              switch (obj.categoryType) {
                case 'Feed':
                  selectedMonthData[selectedMonth].weeks.forEach((obj) => {
                    feedPurchaseValues[Number(Object.keys(obj)[0]) - 1] = obj[Object.keys(obj)[0]]
                  })
                  break
                case 'Non Feed':
                  selectedMonthData[selectedMonth].weeks.forEach((obj) => {
                    nonFeedPurchaseValues[Number(Object.keys(obj)[0]) - 1] = obj[Object.keys(obj)[0]]
                  })
                  break
              }
            }
            setFeedPurchase(feedPurchaseValues) // Adjust month index
            setNonFeedPurchase(nonFeedPurchaseValues) // Adjust month index
          }
        })
      }
    },
    [data, currentYear]
  )

  useEffect(() => {
    if (data.categoryTypeSales) {
      populateMonthValues(data, setFeedPurchaseValues, setNonFeedPurchaseValues, 'FeedAndNonFeedPurchase')
    }
  }, [data, currentYear, currentMonthIndex, populateMonthValues])

  useEffect(() => {
    if (!data?.categoryTypeSales) return
    const salesData = data.sales[currentYear]

    if (selectedValue === 'All') {
      setLabels(getMonthsArray())
      populateMonthValues(data, setFeedPurchaseValues, setNonFeedPurchaseValues, 'FeedAndNonFeedPurchase')
      return
    }
    const weeks = getWeeksInMonth(selectedValue, currentYear)
    setLabels(weeks)
    populateWeekValues(data, setFeedPurchaseValues, setNonFeedPurchaseValues, weeks.length, selectedValue)
  }, [selectedValue, currentYear, data, populateWeekValues, populateMonthValues])

  const series = [
    {
      name: 'Feed',
      type: 'bar',
      data: feedPurchaseValues,
      color: '#1A56DB',
    },
    {
      name: 'Non Feed',
      type: 'bar',
      data: nonFeedPurchaseValues,
      color: '#5edc1f',
    },
  ]

  const options = {
    chart: {
      type: 'bar',
      height: 350,
      stacked: true, // Stacked bars
      toolbar: {
        show: false, // Show toolbar for options
      },
      //   zoom: {
      //     enabled: true,
      //   },
    },
    title: {
      text: 'Feed and Non Feed Purchases', // Chart title
      align: 'left',
      offsetY: -5,
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#333', // Customize title color
        fontFamily: 'Inter, sans-serif',
      },
    },
    tooltip: {
      enabled: true,
      x: {
        show: false,
      },
    },
    dataLabels: {
      enabled: true, // Enable data labels
      style: {
        fontSize: '0.6rem',
        fontWeight: 'bold',
        colors: ['#000'],
      },
      offsetY: -3,
      formatter: function (value, series) {
        return value >= 1000 ? (value / 1000).toFixed(1) : value ? value : ''
      },
    },
    grid: {
      show: true,
      strokeDashArray: 4,
      padding: {
        left: 2,
        right: 2,
        top: -15,
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          legend: {
            position: 'bottom',
            offsetX: -10,
            offsetY: 0,
          },
          title: {
            style: {
              fontSize: '16px',
            },
          },
          dataLabels: {
            style: {
              fontSize: '0.4rem',
            },
          },
          plotOptions: {
            bar: {
              dataLabels: {
                total: {
                  enable: false,
                  style: {
                    fontSize: '0px',
                  },
                },
                // orientation: 'vertical',
                position: 'top',
              },
            },
          },
        },
      },
    ],
    plotOptions: {
      bar: {
        horizontal: false, // Vertical bars
        dataLabels: {
          total: {
            enabled: true, // Show total for stacked bars
            style: {
              fontSize: '12.6px',
              color: '#000',
              fontWeight: 'bold',
            },
          },
        },
      },
    },
    xaxis: {
      categories: labels, // Adjust x-axis labels
      tickAmount: labels.length,
      labels: {
        show: true,
        style: {
          fontFamily: 'Inter, sans-serif',
          cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400',
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
    },
    legend: {
      position: 'bottom',
      formatter: function (value, series) {
        return value + ' (in Thousands)'
      },
    },
    fill: {
      opacity: 1,
    },
  }
  return <ApexChart options={options} series={series} type="bar" />
}
