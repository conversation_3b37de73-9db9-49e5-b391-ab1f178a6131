import connectedDB from '@/app/config/database'
import LiquidationByRetailer from '@/app/models/LiquidationByRetailer'
// GET /api/liquidationByRetailer/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()
    const liquidation = await LiquidationByRetailer.findOne({ partnerId: params.customerId })

    if (!liquidation) return new Response(JSON.stringify({ results: [] }), { status: 200 })
    return new Response(JSON.stringify({ results: liquidation }), { status: 200 })
  } catch (error) {
    return new Response(error, { status: 500 })
  }
}
