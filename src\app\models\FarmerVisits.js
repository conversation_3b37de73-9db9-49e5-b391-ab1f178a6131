import { Schema, model, models } from 'mongoose'

const FarmerVisitsSchema = new Schema(
  {
    farmerName: String,
    location: String,
    farmType: String,
    doc: Number,
    farmSize: Number,
    mobileNumber: String,
    speicesType: String,
    productUsed: String,
    harvestDirectly: String,
    createdDateTime: Date,
    farmerId: String,
    pondId: String,
    partnerId: String,
  },
  { collection: 'FarmerVisits' }
)

const FarmerVisits = models.FarmerVisits || model('FarmerVisits', FarmerVisitsSchema)

export default FarmerVisits
