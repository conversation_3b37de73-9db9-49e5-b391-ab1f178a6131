import { Schema, model, models } from 'mongoose'

const SMRReportSchema = new Schema(
  {
    customerId: String,
    customerName: String,
    date: String,
    date1: String,
    creditNoteId: String,
    creditNoteNumber: String,
    invoiceId: String,
    productId: String,
    itemName: String,
    brand: String,
    itemCategory: String,
    categoryType: String,
    retailType: String,
    invoiceRaisedBy: String,
    businessVertical: String,
    customerType: String,
    ao: String,
    asm: String,
    quantity: String,
    amount: String,
    purchaseValue: String,
    purchasePrice: String,
    margin: String,
    tag: String,
    category: String,
    week: String,

    so: String,
    partner: String,
    partnerId: String,
    productName: String,
    startDate: Date,
    lastDate: Date,
    openingBalance: Number,
    invoice: Number,
    srn: Number,
    closingBalance: Number,
    sales: Number,
    invoiceAmount: Number,
    status: String,
  },
  { collection: 'SMRReport' }
)

const SMRReport = models.SMRReport || model('SMRReport', SMRReportSchema)

export default SMRReport
