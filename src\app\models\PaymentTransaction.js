import { Schema, model, models } from 'mongoose'

const PaymentTransactionSchema = new Schema(
  {
    // Zoho Payment Session Details
    payments_session_id: {
      type: String,
      required: true,
      unique: true,
    },
    payment_id: {
      type: String,
      sparse: true, // Only set when payment is completed
    },

    // Transaction Details
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: true,
      default: 'INR',
    },
    description: {
      type: String,
      required: true,
    },
    invoice_number: {
      type: String,
      required: true,
    },

    // Customer Information
    customer_id: {
      type: String,
      required: true,
    },
    customer_name: {
      type: String,
    },
    customer_email: {
      type: String,
    },
    customer_phone: {
      type: String,
    },

    // Payment Status
    status: {
      type: String,
      enum: ['created', 'pending', 'succeeded', 'failed', 'cancelled', 'expired'],
      default: 'created',
    },
    payment_method: {
      type: String,
    },

    // Metadata
    meta_data: [
      {
        key: String,
        value: String,
      },
    ],

    // URLs
    redirect_url: {
      type: String,
    },
    webhook_url: {
      type: String,
    },

    // Timestamps
    session_created_time: {
      type: Date,
    },
    payment_completed_time: {
      type: Date,
    },
    session_expires_at: {
      type: Date,
    },

    // Webhook Events
    webhook_events: [
      {
        event_type: String,
        event_data: Schema.Types.Mixed,
        received_at: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    // Error Information
    error_code: {
      type: String,
    },
    error_message: {
      type: String,
    },

    // Reference Information
    reference_id: {
      type: String,
    },
    order_id: {
      type: String,
    },

    // Refund Information
    refunds: [
      {
        refund_id: String,
        amount: Number,
        status: String,
        created_at: Date,
        reason: String,
      },
    ],

    // Internal tracking
    created_by: {
      type: String,
    },
    updated_by: {
      type: String,
    },
  },
  {
    collection: 'PaymentTransactions',
    timestamps: true,
  }
)

// Indexes for efficient queries (only add if not already exists)
if (!PaymentTransactionSchema.indexes().find((index) => index[0].payments_session_id)) {
  PaymentTransactionSchema.index({ payments_session_id: 1 })
}
if (!PaymentTransactionSchema.indexes().find((index) => index[0].payment_id)) {
  PaymentTransactionSchema.index({ payment_id: 1 })
}
if (!PaymentTransactionSchema.indexes().find((index) => index[0].customer_id)) {
  PaymentTransactionSchema.index({ customer_id: 1 })
}
if (!PaymentTransactionSchema.indexes().find((index) => index[0].invoice_number)) {
  PaymentTransactionSchema.index({ invoice_number: 1 })
}
if (!PaymentTransactionSchema.indexes().find((index) => index[0].status)) {
  PaymentTransactionSchema.index({ status: 1 })
}
if (!PaymentTransactionSchema.indexes().find((index) => index[0].created_at)) {
  PaymentTransactionSchema.index({ created_at: -1 })
}
if (!PaymentTransactionSchema.indexes().find((index) => index[0].session_expires_at)) {
  PaymentTransactionSchema.index({ session_expires_at: 1 })
}

// Method to check if session is expired
PaymentTransactionSchema.methods.isSessionExpired = function () {
  return new Date() >= this.session_expires_at
}

// Method to add webhook event
PaymentTransactionSchema.methods.addWebhookEvent = function (eventType, eventData) {
  this.webhook_events.push({
    event_type: eventType,
    event_data: eventData,
    received_at: new Date(),
  })
  return this.save()
}

// Method to update payment status
PaymentTransactionSchema.methods.updateStatus = function (newStatus, additionalData = {}) {
  this.status = newStatus
  this.updated_at = new Date()

  if (additionalData.payment_id) {
    this.payment_id = additionalData.payment_id
  }

  if (additionalData.payment_method) {
    this.payment_method = additionalData.payment_method
  }

  if (newStatus === 'succeeded') {
    this.payment_completed_time = new Date()
  }

  if (additionalData.error_code) {
    this.error_code = additionalData.error_code
    this.error_message = additionalData.error_message
  }

  return this.save()
}

const PaymentTransaction = models.PaymentTransaction || model('PaymentTransaction', PaymentTransactionSchema)

export default PaymentTransaction
