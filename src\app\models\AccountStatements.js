import { Schema, model, models } from 'mongoose'

const AccountStatementsSchema = new Schema(
  {
    txnDate: Date,
    vchType: String,
    particulars: String,
    qty: Number,
    debit: Number,
    credit: Number,
    customerId: String,
    customerName: String,
    invoiceNumber: String,
    tcs: Number,
    total: Number,
    subTotal: Number,
    tds: Number,
    adjustment: Number,
    total1: Number,
  },
  { collection: 'AccountStatement' }
)

const AccountStatements = models.AccountStatement || model('AccountStatement', AccountStatementsSchema)

export default AccountStatements
