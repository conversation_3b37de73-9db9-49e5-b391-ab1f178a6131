import { Badge } from '@/components/badge'
import { Heading, Subheading } from '@/components/heading'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table'
import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import moment from 'moment'

function classNames(...classes) {
  return classes.filter(Boolean).join(' ')
}

export function FarmerDetailView({ open, setOpen, farmer }) {
  console.log('farmer Name: ', farmer.name)

  return (
    <>
      <Dialog open={open} onClose={setOpen} className="relative z-10">
        <DialogBackdrop
          transition
          className="fixed inset-0 hidden bg-gray-200 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:block"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-stretch justify-center text-center md:items-center md:px-2 lg:px-4">
            {/* This element is to trick the browser into centering the modal contents. */}
            <span aria-hidden="true" className="hidden md:inline-block md:h-screen md:align-middle">
              &#8203;
            </span>
            <DialogPanel
              transition
              className="flex w-full transform text-left text-base transition data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in md:my-8 md:max-w-2xl md:px-4 data-[closed]:md:translate-y-0 data-[closed]:md:scale-95 lg:max-w-4xl"
            >
              <div className="relative flex w-full flex-col items-center overflow-hidden bg-white px-4 pb-8 pt-14 shadow-2xl sm:px-6 sm:pt-8 md:p-6 lg:p-8">
                <button
                  type="button"
                  onClick={() => setOpen(false)}
                  className="absolute right-4 top-4 text-gray-400 hover:text-gray-500 sm:right-6 sm:top-8 md:right-6 md:top-6 lg:right-8 lg:top-8"
                >
                  <span className="sr-only">Close</span>
                  <XMarkIcon aria-hidden="true" className="h-6 w-6" />
                </button>
                <div className="flex h-screen w-full flex-1 flex-col">
                  <div className="-mx-4 px-4 pb-8 shadow-sm sm:mx-0 sm:rounded-lg sm:px-8 sm:pb-14 lg:col-span-2 lg:row-span-2 lg:row-end-2 xl:px-16 xl:pb-20">
                    <div className="flex flex-col items-start gap-x-6">
                      <div name="farmerDetails" className="m-10 bg-white pl-5 pt-5">
                        <div className="flex flex-wrap items-end justify-between gap-48">
                          <div className="flex flex-wrap items-center gap-6">
                            <div className="w-32 shrink-0">
                              {/* <img className="aspect-[2/3] rounded-lg shadow" src={farmer.visits[0].image} alt="" /> */}

                              <img
                                className="aspect-[2/3] rounded-lg shadow"
                                src={
                                  farmer.visits[0].image
                                    ? farmer.visits[0].image
                                    : farmer.visits[farmer.visits.length - 1].image
                                      ? farmer.visits[farmer.visits.length - 1].image
                                      : `https://placehold.co/400x600/png?font=roboto&text=${farmer.name[0]}`
                                }
                                alt={`${farmer.name}'s farm visit image`}
                              />
                            </div>
                            <div>
                              <div className="flex flex-wrap items-center gap-x-4 gap-y-2">
                                <Heading>{farmer.name}</Heading>
                                <Badge color={farmer.visits[0].farmType === 'Own' ? 'lime' : 'zinc'}>
                                  {farmer.visits[0].farmType}
                                </Badge>
                              </div>
                              <div className="mt-2 text-sm/6 text-zinc-500">
                                {farmer.visits[farmer.visits.length - 1].shortAddress.trim()}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* <div className="mt-8 grid gap-8 sm:grid-cols-3">
                          <Stat title="Total Liqudation" value="Rs 20000" />
                          <Stat title="Total Visits" value="50" />
                        </div> */}
                        <Subheading className="mt-12">Recent visits</Subheading>
                        <Table className="mt-4 [--gutter:theme(spacing.6)] lg:[--gutter:theme(spacing.10)]">
                          <TableHead>
                            <TableRow>
                              <TableHeader>Visited Date</TableHeader>
                              {/* <TableHeader>Farm Size</TableHeader> */}
                              <TableHeader>Doc</TableHeader>
                              <TableHeader className="text-right">Products</TableHeader>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {farmer.visits.map((visit, index) => (
                              <TableRow key={index} title={moment(visit.visitedDate).format('DD-MM-YYYY')}>
                                <TableCell>{moment(visit.visitedDate).format('DD-MM-YYYY')}</TableCell>
                                {/* <TableCell className="text-zinc-500">{visit.farmSize} acre</TableCell> */}
                                <TableCell>
                                  {Number(visit.doc) + moment().diff(moment(visit.visitedDate), 'days')}
                                </TableCell>
                                <TableCell className="text-right">Dr.Grow</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </>
  )
}
