'use client'
import { Heading, Subheading } from '@/components/heading'
import { retailerAtom } from '@/customComponents/providers'
import { useFetchSupportPersionsData } from '@/utils/requests'
// import { PhoneIcon } from '@heroicons/react/24/solid'
import { ContactCard } from '@/customComponents/ContactCard'
import { useAtomValue } from 'jotai'

const HelpAndSupport = () => {
  const customer = useAtomValue(retailerAtom)
  let supportPersionsDetails = useFetchSupportPersionsData(customer?.customerId ?? null)
  const people = []

  return (
    <div>
          <div className="pb-6 mt-2 align-baseline sm:flex sm:justify-between">
        <div className="flex flex-col sm:flex-row">
          <p className="text-2xl font-bold">Help and Support</p>
        </div>
      </div>

      <div className="">
        <Subheading>Your dedicated Aquaconnect Officer</Subheading>
        {supportPersionsDetails && supportPersionsDetails.results && (
          <ContactCard
            person={
              supportPersionsDetails.results[0]?.SO ?? {
                userName: 'name-N/A',
                email: '-',
                userProfile: 'Support',
                mobile: 'N/A',
                imageUrl: '',
              }
            }
          />
        )}
      </div>
      <div className="mt-6">
        <Subheading>For Escalations</Subheading>
        {supportPersionsDetails && supportPersionsDetails.results && (
          <ContactCard
            person={
              supportPersionsDetails.results[0]?.ASM ?? {
                userName: 'name-N/A',

                email: '-',
                userProfile: 'Support',
                mobile: 'N/A',
                imageUrl: '',
              }
            }
          />
        )}
      </div>
      <div className="mt-6">
        <Subheading>For other Queries</Subheading>
        <ContactCard
          person={{
            userName: 'Aqua000 - Aquaconnect (Support)',

            email: '<EMAIL>',
            userProfile: 'Support',
            mobile: '1800 123 1263',
            imageUrl: '',
          }}
        />
      </div>
    </div>
  )
}

export default HelpAndSupport
