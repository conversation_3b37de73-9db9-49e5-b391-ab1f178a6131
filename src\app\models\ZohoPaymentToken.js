import { Schema, model, models } from 'mongoose'

const ZohoPaymentTokenSchema = new Schema(
  {
    access_token: {
      type: String,
      required: true,
    },
    refresh_token: {
      type: String,
      required: true,
    },
    token_type: {
      type: String,
      default: 'Bearer',
    },
    expires_in: {
      type: Number,
      required: true,
    },
    expires_at: {
      type: Date,
      required: true,
    },
    scope: {
      type: String,
      required: true,
    },
    created_at: {
      type: Date,
      default: Date.now,
    },
    updated_at: {
      type: Date,
      default: Date.now,
    },
    is_active: {
      type: Boolean,
      default: true,
    },
  },
  {
    collection: 'ZohoPaymentTokens',
    timestamps: true,
  }
)

// Index for efficient queries
ZohoPaymentTokenSchema.index({ expires_at: 1 })
ZohoPaymentTokenSchema.index({ is_active: 1 })

// Method to check if token is expired
ZohoPaymentTokenSchema.methods.isExpired = function () {
  return new Date() >= this.expires_at
}

// Method to check if token needs refresh (expires in next 5 minutes)
ZohoPaymentTokenSchema.methods.needsRefresh = function () {
  const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000)
  return fiveMinutesFromNow >= this.expires_at
}

const ZohoPaymentToken = models.ZohoPaymentToken || model('ZohoPaymentToken', ZohoPaymentTokenSchema)

export default ZohoPaymentToken
