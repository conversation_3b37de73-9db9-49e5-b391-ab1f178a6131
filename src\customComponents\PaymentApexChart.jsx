'use client'
import { indianCurrencyFormat } from '@/utils/formats'
import { useAtomValue } from 'jotai'
import { useEffect, useMemo, useState } from 'react'
import { getLabelsInSelectedMonth, getMonthsLabels, getfilteredData } from '../utils/date'
import ApexChart from './ApexChart'
import { dashboardAtom } from './providers'

export const PaymentApexChart = ({ selectedValue }) => {
  const data = useAtomValue(dashboardAtom)
  const [labels, setLabels] = useState(() => getMonthsLabels())
 
  // Initialize arrays with dynamic size based on the current month
  const [paymentValues, setPaymentValues] = useState([])

  useEffect(() => {
    const newLabels = selectedValue === 'All' ? getMonthsLabels() : getLabelsInSelectedMonth(selectedValue)
    setLabels(newLabels)
    const filterData = getfilteredData(data?.payments, selectedValue, 'payments')
    console.log("filtered data : ", filterData)
    setPaymentValues(filterData)
    //setPaymentValues(getfilteredData(data?.payments, selectedValue, 'payments'))
  }, [data, selectedValue])

  const series = useMemo(
    () => [
      {
        name: 'Purchases',
        data: paymentValues,
        color: '#07D6F8',
      },
    ],
    [paymentValues]
  )

  const options = useMemo(
    () => ({
      chart: {
        fontFamily: 'Inter, sans-serif',
        dropShadow: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
      },
      title: {
        text: 'Payments', // Chart title
        align: 'left',
        offsetY: -5,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#333', // Customize title color
          fontFamily: 'Inter, sans-serif',
        },
      },
      tooltip: {
        enabled: true,
        x: {
          show: false,
        },
      },
      dataLabels: {
        enabled: true, // Enable data labels
        style: { colors: ['#000'], fontSize: '12px' },
        offsetX: 40,
        formatter: (value) => indianCurrencyFormat(value),
      },
      stroke: { width: 0, curve: 'smooth' },
      grid: {
        show: true,
        strokeDashArray: 4,
        padding: {
          left: 2,
          right: 2,
          top: -10,
        },
      },
      plotOptions: {
        bar: {
          horizontal: true,
          dataLabels: {
            position: 'top',
          },
        },
      },
      responsive: [
        {
          // breakpoint: 480,
          options: {
            plotOptions: {
              bar: {
                dataLabels: {
                  orientation: 'vertical',
                  position: 'top',
                },
              },
            },
            title: {
              style: {
                fontSize: '16px',
              },
            },
          },
        },
      ],
      xaxis: {
        categories: labels, // assuming `labels` is defined
        position: 'top',
        tickAmount: labels.length, // Ensure all labels are shown
        labels: {
          show: false,
          style: {
            fontFamily: 'Inter, sans-serif',
            cssClass: 'text-xs font-normal fill-gray-500 dark:fill-gray-400',
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },

      yaxis: {
        labels: {
          show: true, // Hide the y-axis labels
        },
        axisBorder: {
          show: false, // Hide the y-axis border
        },
        axisTicks: {
          show: false, // Hide the y-axis ticks
        },
        grid: {
          show: false, // Hide gridlines along the y-axis
        },
      },

      legend: {
        show: false,
        showForSingleSeries: true,
        position: 'bottom',
        horizontalAlign: 'center',
        labels: {
          colors: '#333',
          useSeriesColors: false,
        },
        markers: {
          width: 10,
          height: 10,
          radius: 12,
        },
        fontFamily: 'Inter, sans-serif',
        formatter: function (value, series) {
          // return value + ' (in Thousands)'
          return value
        },
      },
    }),
    [labels]
  )


  return <ApexChart options={options} series={series} type="bar" />
}
