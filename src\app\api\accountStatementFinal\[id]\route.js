import connectedDB from '@/app/config/database'
import AccountStatmentFinal from '@/app/models/AccountStatementFinal'
import OpeningBalance from '@/app/models/OpeningBalance'
import { getFinancialYearStartDate } from '@/utils/date'
import moment from 'moment'
import 'moment-timezone'

// GET /api/accountStatementFinal/:id
export const GET = async (request, { params }) => {
  // Always use Asia/Kolkata timezone for consistency
  const today = moment().tz('Asia/Kolkata')
  const currentMonth = today.month() // Current month (0-11)

  console.log('current month : ', currentMonth)

  // Calculate financial year start date
  const financialStartDate =
    currentMonth >= 3
      ? moment.tz('Asia/Kolkata').startOf('year').month(3).date(1) // April 1 of current year
      : moment.tz('Asia/Kolkata').subtract(1, 'year').startOf('year').month(3).date(1) // April 1 of previous year

  console.log('Financial Start : ', financialStartDate.format('DD-MMM-YYYY'))

  try {
    // Use the timezone-aware version for consistency
    const financialYearStartDate = moment.tz(getFinancialYearStartDate(), 'Asia/Kolkata')
    const financialYearStartDateFormatted = financialYearStartDate.format('DD-MMM-YYYY')

    const openingBalanceEntry = {
      txnDate: financialYearStartDate.toISOString(),
      vchType: 'Opening Balance',
      invoiceNumber: '',
      particulars: '***Opening Balance***',
      debit: 0,
      credit: 0,
      total: 0,
      balance: 0,
    }

    await connectedDB()

    // Use cloned moment objects to prevent mutation issues
    const fyStartDate = financialStartDate.clone()
    const fyEndDate = financialStartDate.clone().add(1, 'year').month(2).endOf('month')

    var customerStatement = await AccountStatmentFinal.aggregate([
      {
        $match: {
          customerId: params.id,
          txnDate: {
            $gte: fyStartDate.toDate(),
            $lte: fyEndDate.toDate(),
          },
        },
      },
      {
        $project: {
          txnDate: 1,
          vchType: 1,
          invoiceNumber: 1,
          particulars: 1,
          amount: 1,
          credit: 1,
          debit: 1,
          _id: 0, // Exclude the `_id` field
        },
      },
      {
        $sort: {
          txnDate: 1, // Sort by txnDate in ascending order
        },
      },
    ])

    // Query for opening balance with exact date string match
    const customerOpeningBalance = await OpeningBalance.findOne({
      customerId: params.id,
      date: financialYearStartDateFormatted,
    })

    let balance = 0

    if (customerOpeningBalance) {
      balance = customerOpeningBalance.openingBalance
    }

    openingBalanceEntry.balance = balance

    let updatedCustomerStatement = customerStatement.map((trans) => {
      balance += trans.debit - trans.credit
      trans.balance = balance
      return trans
    })

    updatedCustomerStatement = [openingBalanceEntry, ...updatedCustomerStatement]

    return new Response(JSON.stringify({ results: updatedCustomerStatement }))
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal Server Error', details: error.message }), { status: 500 })
  }
}