# AquaPartner API Documentation

## Overview

This document provides comprehensive documentation for the AquaPartner API. The API follows RESTful principles and uses JSON for data exchange.

## Base URL

All API endpoints are relative to the base URL:

```
/api
```

## Authentication

Some endpoints require authentication using Firebase authentication. For these endpoints, include the Firebase ID token in the Authorization header:

```
Authorization: Bearer <firebase_id_token>
```

## Error Handling

The API uses standard HTTP status codes to indicate the success or failure of requests:

- `200 OK`: The request was successful
- `201 Created`: The resource was successfully created
- `400 Bad Request`: The request was invalid
- `401 Unauthorized`: Authentication is required
- `403 Forbidden`: The client does not have permission to access the resource
- `404 Not Found`: The requested resource was not found
- `500 Internal Server Error`: An error occurred on the server

Error responses include a JSON object with an `error` field containing a description of the error:

```json
{
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

## API Endpoints

### Customer Data

#### Get Customer Details

```
GET /api/customer/:id
```

Retrieves details for a specific customer.

**Parameters:**
- `id` (path parameter): The customer ID

**Response:**
```json
{
  "customerId": "string",
  "customerCode": "string",
  "customerName": "string",
  "contactName": "string",
  "GSTNo": "string",
  "companyName": "string",
  "mobileNumber": "string",
  "Email": "string",
  "billingAddress": "string",
  "businessVertical": "string",
  "customerType": "string",
  "Status": "string"
}
```

#### Get Customer Address

```
GET /api/address/:id
```

Retrieves the address for a specific customer.

**Parameters:**
- `id` (path parameter): The customer ID

**Response:**
```json
{
  "customerId": "string",
  "address": "string",
  "city": "string",
  "state": "string",
  "pincode": "string"
}
```

#### Get User Details

```
GET /api/getUserDetails/:customerId
```

Retrieves detailed user information for a specific customer.

**Parameters:**
- `customerId` (path parameter): The customer ID

**Response:**
```json
{
  "customer": {
    "customerId": "string",
    "customerName": "string",
    "contactName": "string",
    "mobileNumber": "string",
    "Email": "string",
    "billingAddress": "string"
  }
}
```

### Financial Data

#### Get Account Statement

```
GET /api/accountStatementFinal/:id
```

Retrieves the account statement for a specific customer for the current financial year.

**Parameters:**
- `id` (path parameter): The customer ID

**Response:**
```json
{
  "results": [
    {
      "txnDate": "string (ISO date)",
      "vchType": "string",
      "invoiceNumber": "string",
      "particulars": "string",
      "debit": "number",
      "credit": "number",
      "balance": "number"
    }
  ]
}
```

#### Get Customer Payments

```
GET /api/customerPayments/:customerId
```

Retrieves payment transactions for a specific customer.

**Parameters:**
- `customerId` (path parameter): The customer ID

**Response:**
```json
{
  "results": [
    {
      "paymentsDate": "string",
      "amount": "number",
      "paymentMode": "string",
      "referenceNumber": "string"
    }
  ]
}
```

#### Get Invoices

```
GET /api/invoices/:customerId
```

Retrieves invoices for a specific customer.

**Parameters:**
- `customerId` (path parameter): The customer ID

**Response:**
```json
{
  "results": [
    {
      "invoiceId": "string",
      "invoiceDate": "string (ISO date)",
      "invoiceNumber": "string",
      "invoiceStatus": "string",
      "customerId": "string",
      "ageInDays": "number",
      "ageTier": "string",
      "subTotal": "number",
      "total": "number",
      "balance": "number",
      "deliveryMode": "string",
      "deliveryStatus": "string",
      "dueDate": "string (ISO date)",
      "items": [
        {
          "productId": "string",
          "productName": "string",
          "quantity": "number",
          "rate": "number",
          "amount": "number"
        }
      ]
    }
  ]
}
```

#### Get Credit Notes

```
GET /api/creditNotes/:id
```

Retrieves credit notes for a specific customer.

**Parameters:**
- `id` (path parameter): The customer ID

**Response:**
```json
{
  "results": [
    {
      "date": "string",
      "creditNoteNumber": "string",
      "amount": "number",
      "reason": "string"
    }
  ]
}
```

### Products and Pricing

#### Get Products

```
GET /api/products
```

Retrieves all available products.

**Response:**
```json
[
  {
    "productId": "string",
    "productName": "string",
    "description": "string",
    "category": "string",
    "price": "number",
    "status": "string"
  }
]
```

#### Get Product Catalogue

```
GET /api/productCatalogue/:id
```

Retrieves the product catalogue with active products.

**Parameters:**
- `id` (path parameter): Optional parameter (not used in the implementation)

**Response:**
```json
{
  "results": [
    {
      "productId": "string",
      "productName": "string",
      "description": "string",
      "imageUrl": "string",
      "category": "string",
      "price": "number",
      "status": "string",
      "sortOrder": "number"
    }
  ]
}
```

#### Get Price List

```
GET /api/priceList/:id
```

Retrieves the price list for products.

**Parameters:**
- `id` (path parameter): Optional parameter (not used in the implementation)

**Response:**
```json
{
  "results": [
    {
      "state": "string",
      "productId": "string",
      "productName": "string",
      "price": "number",
      "effectiveDate": "string (ISO date)"
    }
  ]
}
```

### Schemes and Reports

#### Get Scheme Data

```
GET /api/scheme/:customerId
```

Retrieves scheme data for a specific customer.

**Parameters:**
- `customerId` (path parameter): The customer ID

**Response:**
```json
{
  "results": {
    "customerId": "string",
    "schemeName": "string",
    "schemeDetails": "string",
    "startDate": "string (ISO date)",
    "endDate": "string (ISO date)",
    "status": "string"
  }
}
```

#### Get Scheme with Support User Details

```
GET /api/getSchemeWithSupportUserDetails/:customerId
```

Retrieves scheme data along with support personnel details for a specific customer.

**Parameters:**
- `customerId` (path parameter): The customer ID

**Response:**
```json
{
  "customerId": "string",
  "schemeName": "string",
  "schemeDetails": "string",
  "startDate": "string (ISO date)",
  "endDate": "string (ISO date)",
  "status": "string",
  "supportPersons": [
    {
      "role": "string",
      "name": "string",
      "contactNumber": "string",
      "email": "string"
    }
  ]
}
```

#### Get SMR Report

```
GET /api/smrReport/:customerId
```

Retrieves SMR (Sales and Marketing Representative) reports for a specific customer.

**Parameters:**
- `customerId` (path parameter): The customer ID

**Response:**
```json
{
  "results": [
    {
      "partnerId": "string",
      "reportDate": "string (ISO date)",
      "reportTitle": "string",
      "reportContent": "string",
      "status": "string"
    }
  ]
}
```

#### Get Liquidation By Retailer

```
GET /api/liquidationByRetailer/:customerId
```

Retrieves liquidation data for a specific retailer.

**Parameters:**
- `customerId` (path parameter): The customer ID

**Response:**
```json
{
  "results": {
    "partnerId": "string",
    "liquidationDate": "string (ISO date)",
    "amount": "number",
    "status": "string"
  }
}
```

### UI Components

#### Get Carousel Data

```
GET /api/carouselData/:id
```

Retrieves carousel data for the UI.

**Parameters:**
- `id` (path parameter): Optional parameter (not used in the implementation)

**Response:**
```json
{
  "results": [
    {
      "title": "string",
      "description": "string",
      "imageUrl": "string",
      "linkUrl": "string",
      "sortOrder": "number"
    }
  ]
}
```

### Payments

#### Initiate Payment

```
POST /api/initiatePayment
```

Initiates a payment transaction.

**Request Body:**
```json
{
  "amount": "number",
  "invoiceNo": "string"
}
```

**Response:**
```json
{
  "paymentSessionId": "string",
  "paymentUrl": "string"
}
```

### Data Synchronization

#### Sync Invoices

```
POST /api/syncInvoices
```

Synchronizes invoice data with the system.

**Request Body:**
```json
{
  "invoices": [
    {
      "invoiceId": "string",
      "invoiceNumber": "string",
      "invoiceDate": "string (ISO date)",
      "customerId": "string",
      "total": "number",
      "items": [
        {
          "productId": "string",
          "quantity": "number",
          "rate": "number",
          "amount": "number"
        }
      ]
    }
  ]
}
```

**Response:**
```json
{
  "message": "Data received successfully"
}
```

## User Management

#### Create/Update User

```
POST /api/users
```

Creates or updates a user. Requires authentication.

**Headers:**
```
Authorization: Bearer <firebase_id_token>
```

**Request Body:**
```json
{
  "userData": {
    "name": "string",
    "email": "string",
    "role": "string"
  }
}
```

**Response:**
```json
{
  "message": "Data received successfully",
  "data": "Data from Server"
}
```

#### Get User Data

```
GET /api/users/:id
```

Retrieves user data. Requires authentication and the user can only access their own data.

**Headers:**
```
Authorization: Bearer <firebase_id_token>
```

**Parameters:**
- `id` (path parameter): The user ID (must match the authenticated user's ID)

**Response:**
```json
{
  "userData": {
    "name": "string",
    "email": "string",
    "role": "string"
  }
}
```

#### Update User Data

```
PUT /api/users/:id
```

Updates user data. Requires authentication and the user can only update their own data.

**Headers:**
```
Authorization: Bearer <firebase_id_token>
```

**Parameters:**
- `id` (path parameter): The user ID (must match the authenticated user's ID)

**Request Body:**
```json
{
  "userData": {
    "name": "string",
    "email": "string",
    "role": "string"
  }
}
```

**Response:**
```json
{
  "message": "User data updated successfully"
}
```
