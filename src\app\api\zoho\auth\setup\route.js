/**
 * POST /api/zoho/auth/setup
 * Token setup is handled by external backend service
 */
export async function POST() {
  return new Response(
    JSON.stringify({
      error: 'Token setup not available',
      message: 'Zoho Payment tokens are managed by an external backend service',
      details: 'Please contact your system administrator to configure payment tokens',
    }),
    { status: 501 }
  )
}

/**
 * GET /api/zoho/auth/setup
 * Get setup instructions
 */
export async function GET() {
  const instructions = {
    message: 'Zoho Payment OAuth Setup Instructions',
    steps: [
      {
        step: 1,
        title: 'Visit Zoho Developer Console',
        url: 'https://accounts.zoho.in/developerconsole',
        description: 'Go to Zoho Developer Console and create a Self Client',
      },
      {
        step: 2,
        title: 'Generate Authorization Code',
        description: 'In the Generate Code tab, enter the required scopes and generate an authorization code',
        scopes: ['ZohoPay.payments.CREATE', 'ZohoPay.payments.READ', 'ZohoPay.refunds.CREATE', 'ZohoPay.refunds.READ'],
      },
      {
        step: 3,
        title: 'Setup Environment Variables',
        description: 'Ensure these environment variables are set:',
        variables: ['ZOHO_OAUTH_CLIENT_ID', 'ZOHO_OAUTH_CLIENT_SECRET', 'ZOHO_PAY_ACCOUNT_ID'],
      },
      {
        step: 4,
        title: 'Token Management',
        description: 'Tokens are managed by external backend service',
        note: 'Contact system administrator for token configuration',
      },
    ],
    current_config: {
      client_id_set: !!process.env.ZOHO_OAUTH_CLIENT_ID,
      client_secret_set: !!process.env.ZOHO_OAUTH_CLIENT_SECRET,
      account_id_set: !!process.env.ZOHO_PAY_ACCOUNT_ID,
    },
  }

  return new Response(JSON.stringify(instructions), { status: 200 })
}
