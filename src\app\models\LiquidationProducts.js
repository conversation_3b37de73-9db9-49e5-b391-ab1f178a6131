import { Schema, model, models } from 'mongoose'

const LiquidationProductsSchema = new Schema(
  {
    createdBy: String,
    createdDateTime: Date,
    date: Date,
    productId: String,
    productName: String,
    productQty: Number,
    liquidateId: String,
  },
  { collection: 'LiquidationProducts' }
)

const LiquidationProducts = models.LiquidationProducts || model('LiquidationProducts', LiquidationProductsSchema)

export default LiquidationProducts
