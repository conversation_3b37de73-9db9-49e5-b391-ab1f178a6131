'use client'
import { useEffect } from 'react'

const CustomSnackBar = ({ message, type, isVisible, onClose }) => {
  useEffect(() => {
    if (isVisible) {
      // Auto-hide the snack bar after 3 seconds
      const timer = setTimeout(() => {
        onClose()
      }, 5000)

      return () => clearTimeout(timer) // Cleanup on unmount or when isVisible changes
    }
  }, [isVisible, onClose])

  return (
    <div
      className={`fixed bottom-8 left-1/2 -translate-x-1/2 transition-all duration-500 ease-in-out ${
        isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
      } z-50 min-w-[250px] rounded-md px-4 py-3 shadow-lg ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
      } text-center text-white`}
    >
      {message}
    </div>
  )
}

export default CustomSnackBar
